import { Injectable } from '@angular/core';
import { Resolve } from '@angular/router';
import { GameServerService } from 'src/app/pages/game-server/game-server.service';
import { GameServerSettings } from '../../typings/game-server';
import { catchError } from 'rxjs/operators';
import { of } from 'rxjs';

@Injectable()
export class GameServersResolver implements Resolve<GameServerSettings[]> {
  constructor(private service: GameServerService) {
  }

  resolve() {
    return this.service.getList().pipe(
      catchError(() => of([]))
    );
  }
}
