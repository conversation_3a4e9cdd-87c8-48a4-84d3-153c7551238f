import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';

import { HighchartsModule } from '../../common/components/swHighcharts/highcharts.module';

import { Dashboard } from './dashboard.component';
import { DashboardRoutingModule } from './dashboard.routing';

// import { PopularApp } from './popularApp';
// import { PieChart } from './pieChart';
// import { TrafficChart } from './trafficChart';
// import { UsersMap } from './usersMap';
// import { LineChart } from './lineChart';
// import { Feed } from './feed';
// import { Todo } from './todo';
// import { Calendar } from './calendar';
// import { CalendarService } from './calendar/calendar.service';
// import { FeedService } from './feed/feed.service';
// import { LineChartService } from './lineChart/lineChart.service';
// import { PieChartService } from './pieChart/pieChart.service';
// import { TodoService } from './todo/todo.service';
// import { TrafficChartService } from './trafficChart/trafficChart.service';
// import { UsersMapService } from './usersMap/usersMap.service';


@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    DashboardRoutingModule,
    HighchartsModule,
    TranslateModule,
  ],
  declarations: [
    // PopularApp,
    // PieChart,
    // TrafficChart,
    // UsersMap,
    // LineChart,
    // Feed,
    // Todo,
    // Calendar,
    Dashboard
  ],
  providers: [
    // CalendarService,
    // FeedService,
    // LineChartService,
    // PieChartService,
    // TodoService,
    // TrafficChartService,
    // UsersMapService
  ]
})
export class DashboardModule {
}
