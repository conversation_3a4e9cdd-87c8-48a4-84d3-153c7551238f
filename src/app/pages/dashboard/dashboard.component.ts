import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { SwuiNotificationsService } from '@skywind-group/lib-swui';

import { ChartsService } from '../../common/services/charts.service';
import { AVERAGE_PAYMENTS_CHART_SCHEMA, GCR_CHART_SCHEMA, PAYMENTS_CHART_SCHEMA } from './chart-schemas';


@Component({
  selector: 'dashboard',
  encapsulation: ViewEncapsulation.None,
  // styles: [require('./dashboard.styl')],
  templateUrl: './dashboard.html'
})
export class Dashboard implements OnInit {
  isServiceAvailable = true;
  location: any;

  paymentsChart = {
    data: [],
    schema: []
  };
  avgPaymentsChart = {
    data: [],
    schema: []
  };
  gcrChart = {
    data: [],
    schema: []
  };

  constructor( public notifications: SwuiNotificationsService,
               private chartsService: ChartsService,
  ) {
    // this.chart1Config = new PieChartConfig();
    // this.chart1Config.settings = {
    //   fill: 'rgba(1, 67, 163, 1)',
    //   stroke: 'black',
    //   interpolation: 'monotone'
    // };
    // this.chart1Config.dataset = D3.range(10).map(Math.random).sort(D3.descending);
  }

  ngOnInit() {
    // this.biService.getIframeUrl('dashboard')
    //  .then(( data ) => {
    //    this.location = this.sanitizer.bypassSecurityTrustResourceUrl(data.location);
    //  })
    //  .catch(( error ) => {
    //    this.isServiceAvailable = false;
    //  });

    this.paymentsChart.schema = PAYMENTS_CHART_SCHEMA;
    this.chartsService.getPaymentData().subscribe(( data ) => {
      this.paymentsChart.data = data;
    });

    this.avgPaymentsChart.schema = AVERAGE_PAYMENTS_CHART_SCHEMA;
    this.chartsService.getAvgPaymentData().subscribe(( data ) => {
      this.avgPaymentsChart.data = data;
    });

    this.gcrChart.schema = GCR_CHART_SCHEMA;
    this.chartsService.getGcrData().subscribe(( data ) => {
      this.gcrChart.data = data;
    });


  }

}
