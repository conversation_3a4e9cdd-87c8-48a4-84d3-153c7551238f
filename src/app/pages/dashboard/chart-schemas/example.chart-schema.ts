import { setDefaultValues } from '../../../common/components/swHighcharts/helpers';
import { ChartSchemaField, HIGHCHARTS_TYPES } from '../../../common/components/swHighcharts/highcharts.component';

const CHART_SCHEMA: ChartSchemaField[] = [
  {
    configField: 'chart',
    configValue: {
      type: HIGHCHARTS_TYPES.column
    }
  },
  {
    configField: 'series',
    configValue: [
      {
        name: 'Tokyo',
        data: []
      },
      {
        name: 'New York',
        data: []
      },
      {
        name: 'London',
        data: []
      },
      {
        name: 'Berlin',
        data: []
      },
    ]
  },
  {
    configField: 'xAxis',
    configValue: {
      categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
      crosshair: true
    }
  },
  {
    configField: 'yAxis',
    configValue: {
      min: 0,
      title: {
        text: 'Rainfall (mm)'
      }
    }
  },
  {
    configField: 'tooltip',
    configValue: {
      headerFormat: '<span style="font-size:10px">{point.key}</span><table>',
      pointFormat: '<tr><td style="color:{series.color};padding:0">{series.name}: </td>' +
      '<td style="padding:0"><b>{point.y:.1f} mm</b></td></tr>',
      footerFormat: '</table>',
      shared: true,
      useHTML: true
    }
  },
  {
    configField: 'plotOptions',
    configValue: {
      column: {
        pointPadding: 0.2,
        borderWidth: 0
      }
    }
  }
];

export const EXAMPLE_CHART_SCHEMA = setDefaultValues(CHART_SCHEMA);
