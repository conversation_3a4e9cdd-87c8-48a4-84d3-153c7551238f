import { latestMonths, moneyFormatter, setDefaultValues } from '../../../common/components/swHighcharts/helpers';
import { ChartSchemaField, HIGHCHARTS_TYPES } from '../../../common/components/swHighcharts/highcharts.component';

let _ = (e) => e;


const CHART_SCHEMA: ChartSchemaField[] = [
    {
      configField: 'chart',
      configValue:
        {
          type: HIGHCHARTS_TYPES.column
        }
    },
    {
      configField: 'legend',
      configValue: {
        squareSymbol: true,
        symbolRadius: 0
      }
    },
    {
      configField: 'series',
      configValue: [
        {
          title: {
            text: _('CHART.X-AXIS-TITLE-DEFAULT')
          },
          name: _('GGR (EUR)'),
          color: '#4595E4',
          data: [],
          yAxis: 0
        },
        {
          title: {
            text: _('CHART.X-AXIS-TITLE-DEFAULT')
          },
          name: _('RPT (EUR)'),
          color: '#FE8027',
          data: [],
          type: HIGHCHARTS_TYPES.line,
          yAxis: 1
        }
      ]
    },
    {
      configField: 'xAxis',
      configValue: {
        categories: latestMonths(7),
        crosshair: true,
        gridLineDashStyle: 'dash',
        gridLineWidth: 1,
        gridZIndex: 4
      }
    },
    {
      configField: 'yAxis',
      configValue: [{
        title: {
          text: _('CHART.X-AXIS-TITLE-DEFAULT')
        },
        min: 0,
        labels: {
          formatter: function () {
            return `\u20AC${moneyFormatter(this.value, 2)}`;
          }
        },
        gridLineDashStyle: 'dash'
      }, {
        gridLineWidth: 0,
        min: 0,
        max: 1,
        labels: {
          formatter: function () {
            return `${this.value * 100}%`;
          }
        },
        opposite: true
      }]
    },
    {
      configField: 'tooltip',
      configValue: {
        headerFormat: '<span style="font-size:10px">{point.key}</span><table>',
        pointFormat: '<tr><td style="color:{series.color};padding:0">{series.name}: </td>' +
        '<td style="padding:0"><b>{point.y}</b></td></tr>',
        footerFormat: '</table>',
        shared: true,
        useHTML: true
      }
    },
    {
      configField: 'plotOptions',
      configValue: {
        series: {
          dataLabels: {
            enabled: true,
            allowOverlap: true,
            crop: false,
            formatter: function () {
              let result: string = '';
              if (this.series.name === 'GGR (EUR)') {
                result = `\u20AC${moneyFormatter(this.y, 2)}`;
              } else {
                result = (parseFloat(this.y) * 100).toFixed(2) + '%';
              }
              return result;
            }
          }
        }
      }
    }
  ]
;

export const GCR_CHART_SCHEMA = setDefaultValues(CHART_SCHEMA);
