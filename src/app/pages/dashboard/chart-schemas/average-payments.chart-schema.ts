import { latestMonths, moneyFormatter, setDefaultValues } from '../../../common/components/swHighcharts/helpers';
import { ChartSchemaField, HIGHCHARTS_TYPES } from '../../../common/components/swHighcharts/highcharts.component';

const CHART_SCHEMA: ChartSchemaField[] = [
  {
    configField: 'chart',
    configValue: {
      type: HIGHCHARTS_TYPES.column
    }
  },
  {
    configField: 'legend',
    configValue: {
      squareSymbol: true,
      symbolRadius: 0
    }
  },
  {
    configField: 'series',
    configValue: [
      {
        name: 'Avg Bet (EUR)',
        color: '#4595E4',
        data: []
      },
      {
        name: 'Avg Win (EUR)',
        color: '#FB746F',
        data: []
      }
    ]
  },
  {
    configField: 'xAxis',
    configValue: {
      categories: latestMonths(7),
      crosshair: true,
      gridLineDashStyle: 'dash',
      gridLineWidth: 1,
      gridZIndex: 4
    }
  },
  {
    configField: 'yAxis',
    configValue: {
      min: 0,
      labels: {
        formatter: function () {
          return `\u20AC${moneyFormatter(this.value, 2)}`;
        }
      },
      gridLineDashStyle: 'dash'
    }
  },
  {
    configField: 'tooltip',
    configValue: {
      headerFormat: '<span style="font-size:10px">{point.key}</span><table>',
      pointFormat: '<tr><td style="color:{series.color};padding:0">{series.name}: </td>' +
      '<td style="padding:0"><b>{point.y}</b></td></tr>',
      footerFormat: '</table>',
      shared: true,
      useHTML: true
    }
  },
  {
    configField: 'plotOptions',
    configValue: {
      series: {
        dataLabels: {
          enabled: true,
          allowOverlap: true,
          crop: false,
          rotation: -45,
          padding: 0,
          y: -12,
          x: 7,
          formatter: function () {
            return `\u20AC${moneyFormatter(this.y, 2)}`;
          }
        }
      }
    }
  }
];

export const AVERAGE_PAYMENTS_CHART_SCHEMA = setDefaultValues(CHART_SCHEMA);
