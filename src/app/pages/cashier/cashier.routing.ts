import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

import { PERMISSIONS_NAMES } from '../../app.constants';
import { BriefResolver } from '../../common/services/resolvers/brief.resolver';
import { CashierComponent } from './cashier.component';


const routes: Routes = [
  {
    path: '',
    component: CashierComponent,
    data: {
      permissions: [PERMISSIONS_NAMES.KEYENTITY_CASHIER],
      title: 'Cashier'
    },
    resolve: {
      brief: BriefResolver,
    },
    children: [],
  }
];

@NgModule({
  imports: [
    RouterModule.forChild(routes)
  ],
  exports: [
    RouterModule,
  ]
})
export class CashierRoutingModule {
}
