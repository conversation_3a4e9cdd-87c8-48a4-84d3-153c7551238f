<lib-swui-page-panel [title]="'MENU_SECTIONS.cashier'"></lib-swui-page-panel>
<div class="cashier-body">
  <div *ngIf="playerRequestState.status != 'none'" class=" margin-bottom12 custom-error" [ngClass]="{
  'custom-error--warn': playerRequestState.status === 'pending',
  'custom-error--error': playerRequestState.status === 'error'
  }">
    <div [ngSwitch]="playerRequestState.status">
      <span *ngSwitchCase="'pending'">{{ 'CASHIER.lookingForPlayer' | translate }}</span>
      <span *ngSwitchCase="'error'">{{ 'CASHIER.playerNotFound' | translate }}</span>
    </div>
  </div>

  <form [formGroup]="form">
    <mat-form-field appearance="outline" class="width100">
      <mat-label>{{ 'CASHIER.playerUsername' | translate }}</mat-label>
      <input matInput trimValue type="text"
             [formControl]=playerCodeControl>
      <mat-error>
        <lib-swui-control-messages
          [messages]="messageErrors"
          [control]="playerCodeControl">
        </lib-swui-control-messages>
      </mat-error>
    </mat-form-field>

    <mat-form-field appearance="outline" class="width100">
      <mat-label>{{ 'CASHIER.amount' | translate }}</mat-label>
      <input matInput
             type="number" min="0.01" step="0.01"
             [formControl]=amountControl>
      <mat-error>
        <lib-swui-control-messages
          [messages]="messageErrors"
          [control]="amountControl">
        </lib-swui-control-messages>
      </mat-error>
    </mat-form-field>
  </form>
</div>

<div fxLayout="row" fxLayoutAlign="end center" class="cashier-actions">
  <button mat-button color="primary"
          (click)="transfer('in')"
          [disabled]="playerRequestState.status !== 'ok' || !depositAvailable">
    {{ 'CASHIER.deposit' | translate }}</button>
  <button mat-button class="margin-left4" color="primary"
          (click)="transfer('out')"
          [disabled]="playerRequestState.status !== 'ok' || loadingPlayer || !withdrawalAvailable">
    {{ 'CASHIER.cashout' | translate }}</button>
  <button mat-stroked-button class="margin-left4" color="primary"
          [routerLink]="['/pages/players', player?.code]"
          [disabled]="!player || !viewAvailable">
    {{ 'CASHIER.seePlayerInfo' | translate }}</button>
  <button mat-flat-button class="margin-left4" color="primary"
          [disabled]="!createAvailable"
          (click)="createPlayer()">
    {{ 'CASHIER.createPlayer' | translate }}</button>
</div>

<div *ngIf="player">
  <player-info [player]="player"></player-info>
</div>
