import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { TranslateService } from '@ngx-translate/core';
import { PERMISSIONS_NAMES, SwHubAuthService, SwuiNotificationsService } from '@skywind-group/lib-swui';
import { merge, of, Subject } from 'rxjs';
import {
  catchError,
  debounceTime,
  distinctUntilChanged,
  filter,
  finalize,
  map,
  pluck,
  share,
  switchMap,
  takeUntil,
  tap
} from 'rxjs/operators';
import { ErrorMessage } from '../../common/components/mat-user-editor/user-form.component';
import { TransferData } from '../../common/models/customer-balance.model';
import { PlayerService } from '../../common/services/player.service';
import { ValidationService } from '../../common/services/validation.service';
import { Player } from '../../common/typings';
import { CreatePlayerDialogComponent } from '../player/components/list/dialogs/create-player-dialog.component';
import { ActivatedRoute } from '@angular/router';


@Component({
  selector: 'cashier',
  templateUrl: './cashier.component.html',
  styleUrls: ['./cashier.component.scss']
})
export class CashierComponent implements OnInit {
  form: FormGroup;
  player: Player;

  messageErrors: ErrorMessage = {
    required: 'VALIDATION.required',
    invalidLatinCharsDigitsSymbols: `VALIDATION.invalidLatinCharsDigitsSymbols`,
    min: 'VALIDATION.min'
  };

  playerRequestState: {
    status: 'none' | 'pending' | 'error' | 'ok';
  } = { status: 'none' };

  loadingPlayer: boolean = false;
  depositAvailable: boolean;
  withdrawalAvailable: boolean;
  viewAvailable: boolean;
  createAvailable: boolean;
  playerPrefix: string;

  private readonly destroyed$ = new Subject<void>();
  private playerUpdate = new Subject<string>();

  constructor( fb: FormBuilder,
               private dialog: MatDialog,
               private translate: TranslateService,
               private playerService: PlayerService,
               private notifications: SwuiNotificationsService,
               { snapshot: { data: { brief } } }: ActivatedRoute,
               auth: SwHubAuthService
  ) {
    this.depositAvailable = auth.areGranted([PERMISSIONS_NAMES.KEYENTITY_PLAYER_DEPOSIT]);
    this.withdrawalAvailable = auth.areGranted([PERMISSIONS_NAMES.KEYENTITY_PLAYER_WITHDRAWAL]);
    this.viewAvailable = auth.areGranted([PERMISSIONS_NAMES.KEYENTITY_PLAYER_VIEW]);
    this.createAvailable = auth.areGranted([PERMISSIONS_NAMES.KEYENTITY_PLAYER_CREATE]);
    this.playerPrefix = brief.settings.playerPrefix;

    this.form = fb.group({
      playerCode: [
        '', Validators.compose([
          Validators.required,
          ValidationService.latinCharsDigitsSymbols
        ])
      ],
      amount: ['', Validators.compose([Validators.required, Validators.min(0.01)])]
    });

    if (!this.viewAvailable) {
      this.form.disable();
    }
  }

  ngOnInit(): void {
    const $playerCode = this.playerCodeControl.valueChanges;

    const $playerCodeChanged = merge(
      this.playerUpdate,
      $playerCode.pipe(
        debounceTime(1000),
        distinctUntilChanged(),
      )
    );

    $playerCodeChanged
      .pipe(
        filter(code => code === '')
      )
      .subscribe(() => {
        this.player = undefined;
        this.playerRequestState.status = 'none';
      });

    const $playerUpdate = $playerCodeChanged
      .pipe(
        filter(code => code !== '')
      );

    const $getPlayerInfo = $playerUpdate
      .pipe(
        switchMap(( code, index ) => {
          console.log('code  index\n', code, ' ', index);
          return this.playerService.getItem({ id: code }, true)
            .pipe(
              map(player => ({ status: 'ok', player })),
              catchError(err => of({ status: 'error', err }))
            );
        }),
        share()
      );

    $getPlayerInfo
      .subscribe(
        ( data: any ) => {
          this.playerRequestState.status = data.status;
        }
      );

    $getPlayerInfo
      .subscribe(( data: any ) => this.player = data.status === 'ok' ? data.player : undefined);
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  get playerCodeControl(): FormControl {
    return this.form.get('playerCode') as FormControl;
  }

  get amountControl(): FormControl {
    return this.form.get('amount') as FormControl;
  }

  createPlayer() {
    this.dialog.open(CreatePlayerDialogComponent, {
      width: '600px',
      data: {
        player: {},
        prefix: this.playerPrefix
      },
      disableClose: true
    }).afterClosed().pipe(
      filter(data => !!data),
      switchMap(( player: Player ) => this.playerService.register(player)),
      takeUntil(this.destroyed$)
    ).subscribe();
  }

  transfer( direction: 'in' | 'out' ) {
    this.amountControl.markAsTouched();
    this.amountControl.updateValueAndValidity();

    if (this.form.valid) {
      const transfer: TransferData = Object.assign(this.form.getRawValue(), {
        player: this.player,
        direction: direction,
      });

      of(direction)
        .pipe(
          switchMap(dir => dir === 'out' ? this.checkPlayerOffline() : of(true)),
          filter(( openTransfer: boolean ) => openTransfer),
          switchMap(() => this.playerService.transfer(transfer)),
          tap(() => this.notifications.success(this.translate.instant('CUSTOMERS.NOTIFICATIONS.transfer_success'))),
          takeUntil(this.destroyed$)
        ).subscribe(() => this.updatePlayerInfo());
    }
  }

  updatePlayerInfo() {
    this.playerUpdate.next(this.playerCodeControl.value);
  }

  private checkPlayerOffline() {
    this.loadingPlayer = true;
    return this.playerService.getItem({ id: this.player.code }, true).pipe(
      finalize(() => this.loadingPlayer = false),
      map(( player: Player ) => this.player = player),
      tap(( player: Player ) => player.isOnline ?
        this.notifications.error(this.translate.instant('CUSTOMERS.playerIsOnline')) : ''),
      pluck('isOnline'),
      map(online => !online)
    );
  }
}
