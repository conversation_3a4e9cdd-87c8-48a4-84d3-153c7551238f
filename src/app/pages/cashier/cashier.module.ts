import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FlexModule } from '@angular/flex-layout';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiControlMessagesModule, SwuiPagePanelModule } from '@skywind-group/lib-swui';
import { CurrencyService } from '../../common/services/currency.service';
import { LanguagesService } from '../../common/services/languages.service';
import { PlayerService } from '../../common/services/player.service';
import { CashierComponent } from './cashier.component';
import { PlayerInfoComponent } from './player-info/player-info.component';
import { CashierRoutingModule } from './cashier.routing';
import { CountryService } from 'src/app/common/services/country.service';


@NgModule({
  declarations: [
    CashierComponent,
    PlayerInfoComponent
  ],
  exports: [
    CashierComponent
  ],
  imports: [
    CommonModule,
    SwuiPagePanelModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    TranslateModule,
    SwuiControlMessagesModule,
    MatButtonModule,
    FlexModule,
    RouterModule,
    CashierRoutingModule
  ],
  providers: [
    PlayerService,
    CurrencyService,
    CountryService,
    LanguagesService
  ]
})
export class CashierModule {
}
