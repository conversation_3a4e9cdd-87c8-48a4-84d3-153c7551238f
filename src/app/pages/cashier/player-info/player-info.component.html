<div class="player-info-body">
  <div fxLayout="row" class="margin-bottom16">
    <div>
      <img [src]="noImageAvatar" alt="Avatar">
    </div>

    <div class="margin-left8">
      <h6>
        <span>{{ player?.login }} - {{ player?.firstName }} {{ player?.lastName }}</span>
        <span class="margin-left4 sw-chip {{ player?.isOnline ? 'sw-chip-blue':'sw-chip' }}">
          {{ player?.isOnline ? 'online' : 'offline' }}
        </span>
      </h6>
      <span>
        {{ player?.isTest ? 'Test account' : 'Real account' }}
      </span>
    </div>
  </div>

  <form [formGroup]="form">
    <div fxLayout="row" class="width100">
      <div fxLayout="column" fxFlex="50">
        <mat-form-field appearance="outline">
          <mat-label>{{ 'CASHIER.balance' | translate }}:</mat-label>
          <input matInput trimValue type="text" formControlName='balances'>
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>{{ 'CASHIER.customerID' | translate }}:</mat-label>
          <input matInput trimValue type="text" formControlName='code'>
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>{{ 'CASHIER.firstName' | translate }}:</mat-label>
          <input matInput trimValue type="text" formControlName='firstName'>
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>{{ 'CASHIER.lastName' | translate }}:</mat-label>
          <input matInput trimValue type="text" formControlName='lastName'>
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>{{ 'CASHIER.login' | translate }}:</mat-label>
          <input matInput trimValue type="text" formControlName='login'>
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>{{ 'CASHIER.email' | translate }}:</mat-label>
          <input matInput trimValue type="text" formControlName='email'>
        </mat-form-field>
      </div>

      <div fxLayout="column" fxFlex="50" class="margin-left16">
        <mat-form-field appearance="outline">
          <mat-label>{{ 'CASHIER.currency' | translate }}:</mat-label>
          <input matInput trimValue type="text" formControlName='currency'>
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>{{ 'CASHIER.status' | translate }}:</mat-label>
          <input matInput trimValue type="text" formControlName='status'>
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>{{ 'CASHIER.type' | translate }}:</mat-label>
          <input matInput trimValue type="text" formControlName='isTest'>
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>{{ 'CASHIER.profileUpdate' | translate }}:</mat-label>
          <input matInput trimValue type="text" formControlName='updatedAt'>
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>{{ 'CASHIER.lastLogin' | translate }}:</mat-label>
          <input matInput trimValue type="text" formControlName='lastLogin'>
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>{{ 'CASHIER.registered' | translate }}:</mat-label>
          <input matInput trimValue type="text" formControlName='createdAt'>
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>{{ 'CASHIER.country' | translate }}:</mat-label>
          <input matInput trimValue type="text" formControlName='country'>
        </mat-form-field>
      </div>
    </div>
  </form>
</div>
