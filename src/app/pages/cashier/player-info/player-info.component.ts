import { Component, Input, OnInit } from '@angular/core';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { SettingsService } from '@skywind-group/lib-swui';
import moment from 'moment';
import { Player } from '../../../common/typings';

@Component({
  selector: 'player-info',
  templateUrl: './player-info.component.html',
  styleUrls: ['./player-info.component.scss']
})
export class PlayerInfoComponent implements OnInit {

  @Input()
  set player( val: Player ) {
    if (!val) return;
    this._player = val;
    this.updateForm(val);
  }

  get player(): Player {
    return this._player;
  }

  noImageAvatar = 'data:image/png;base64,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';
  form: FormGroup;

  private _player: Player;

  constructor( private fb: FormBuilder,
               private setting: SettingsService
  ) {
    this.form = this.fb.group({
      balances: '',
      currency: '',
      code: '',
      firstName: '',
      lastName: '',
      login: '',
      email: '',
      status: '',
      isTest: '',
      updatedAt: '',
      lastLogin: '',
      createdAt: '',
      country: '',
    });

    this.form.disable();
  }

  ngOnInit(): void {
  }

  get balancesControl(): FormControl {
    return this.form.get('balances') as FormControl;
  }

  get playerTypeControl(): FormControl {
    return this.form.get('isTest') as FormControl;
  }

  get playerStatusControl(): FormControl {
    return this.form.get('status') as FormControl;
  }

  get playerUpdatedAtControl(): FormControl {
    return this.form.get('updatedAt') as FormControl;
  }


  get playerLastLoginControl(): FormControl {
    return this.form.get('lastLogin') as FormControl;
  }


  get playerCreatedAtControl(): FormControl {
    return this.form.get('createdAt') as FormControl;
  }

  private updateForm( player: Player ) {
    this.form.patchValue(player);
    this.setPlayerBalance(player);
    this.setPlayerType(player);
    this.setPlayerStatus(player);
    this.convertDate(player);
  }

  private setPlayerBalance( player: Player ) {
    this.balancesControl.patchValue(player.balance);
  }

  private setPlayerType( player: Player ) {
    const type = player.isTest ? 'Test' : 'Real';
    this.playerTypeControl.patchValue(type);
  }

  private setPlayerStatus( player: Player ) {
    const type = player?.status === 'normal' ? 'Active' : 'Frozen';
    this.playerStatusControl.patchValue(type);
  }

  private convertDate( player: Player ) {
    const dateFormat = this.setting.appSettings.dateFormat;
    const timeFormat = this.setting.appSettings.timeFormat;

    this.playerUpdatedAtControl.patchValue(
      moment(player.updatedAt).format(`${dateFormat} ${timeFormat}`));

    this.playerLastLoginControl.patchValue(
      moment(player.lastLogin).format(`${dateFormat} ${timeFormat}`));

    this.playerCreatedAtControl.patchValue(
      moment(player.createdAt).format(`${dateFormat} ${timeFormat}`));
  }
}
