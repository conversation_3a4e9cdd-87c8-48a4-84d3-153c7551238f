import { HttpErrorResponse } from '@angular/common/http';
import { Component, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { TranslateService } from '@ngx-translate/core';
import {
  PanelAction, RowAction, SwuiGridComponent, SwuiGridDataService, SwuiGridField, SwuiNotificationsService, SwuiTopFilterDataService
} from '@skywind-group/lib-swui';
import { Subject, throwError } from 'rxjs';
import { catchError, filter, switchMap, takeUntil, tap } from 'rxjs/operators';
import { BoConfirmationComponent, ConfirmationDialogData } from '../../common/components/bo-confirmation/bo-confirmation.component';

import { GameServerSettings } from '../../common/typings/game-server';
import { GameServerService } from './game-server.service';
import { GameServerModalComponent } from './modals/game-server-modal.component';
import { SCHEMA_LIST } from './schema';


@Component({
  selector: 'game-server',
  templateUrl: './game-server.component.html',
  providers: [
    SwuiTopFilterDataService,
    { provide: SwuiGridDataService, useClass: GameServerService }
  ]
})

export class GameServerComponent {
  public componentTitle: string = 'GAME_SERVER.title';

  public schema: SwuiGridField[] = <SwuiGridField[]>SCHEMA_LIST;
  public panelActions: PanelAction[] = [];
  public rowActions: RowAction[] = [];

  @ViewChild('grid', { static: true }) public grid: SwuiGridComponent<GameServerSettings>;
  private destroyed$ = new Subject<void>();

  constructor( private notifications: SwuiNotificationsService,
               private translate: TranslateService,
               private dialog: MatDialog,
               private service: GameServerService
  ) {
  }

  ngOnInit() {
    this.setPanelActions();
    this.setRowActions();
  }

  ngOnDestroy() {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  private setRowActions() {
    this.rowActions = [
      new RowAction(
        {
          title: 'GAME_SERVER.editServer',
          icon: 'edit',
          fn: ( item: GameServerSettings ) => {
            this.dialog.open(GameServerModalComponent, {
              width: '500px',
              data: item,
              disableClose: true,
              maxHeight: '95vh'
            }).afterClosed().pipe(
              filter(data => !!data),
              switchMap(( data: GameServerSettings ) => this.service.updateItem(item.name, data)),
              switchMap(() => this.translate.get('GAME_SERVER.notificationConfigSaved')),
              tap(message => this.notifications.success(message, '')),
              takeUntil(this.destroyed$)
            ).subscribe(() => this.grid.dataSource.loadData());
          },
          canActivateFn: () => true,
        }),
      new RowAction({
        title: 'GAME_SERVER.removeServer',
        icon: 'delete',
        fn: ( item: GameServerSettings ) => {
          this.dialog.open<BoConfirmationComponent, ConfirmationDialogData, boolean>(
            BoConfirmationComponent,
            {
              data: { message: 'GAME_SERVER.messageRemove' },
              disableClose: true
            }
          ).afterClosed().pipe(
            filter(data => !!data),
            switchMap(() => this.service.deleteItem(item.name)),
            switchMap(() => this.translate.get('GAME_SERVER.notificationConfigRemoved')),
            tap(message => this.notifications.success(message, '')),
            takeUntil(this.destroyed$)
          ).subscribe(() => this.grid.dataSource.loadData());
        },
        canActivateFn: () => true,
      })
    ];
  }

  private setPanelActions() {
    this.panelActions.push({
      title: 'GAME_SERVER.createNew',
      color: 'primary',
      icon: 'add',
      actionFn: () => this.dialog.open(GameServerModalComponent,
        {
          width: '500px',
          data: this.translate.instant('GAME_SERVER.configPlaceholder'),
          disableClose: true,
          maxHeight: '95vh'
        }).afterClosed().pipe(
        filter(data => !!data),
        switchMap(( data: GameServerSettings ) => this.service.createItem(data)),
        switchMap(() => this.translate.get('GAME_SERVER.notificationConfigSaved')),
        catchError(( error: HttpErrorResponse ) => {
          this.notifications.error(error?.error?.message);
          return throwError(error);
        }),
        tap(message => this.notifications.success(message, '')),
        takeUntil(this.destroyed$)
      ).subscribe(() => this.grid.dataSource.loadData())
    });
  }
}
