import { SchemaFilterMatchEnum, SwuiGridField } from '@skywind-group/lib-swui';

const SCHEMA: SwuiGridField[] = [
  {
    field: 'name',
    title: 'GAME_SERVER.GRID.name',
    type: 'string',
    dataSource: '',
    isList: true,
    isViewable: true,
    isSortable: false,
    isFilterable: false,
    isEditable: false,
    filterMatch: SchemaFilterMatchEnum.Contains,
  },
  {
    field: 'description',
    title: 'GAME_SERVER.GRID.description',
    type: 'string',
    isList: true,
    isViewable: true,
    isSortable: false,
    isFilterable: false,
    filterMatch: SchemaFilterMatchEnum.Contains,
  },
  {
    field: 'roundIdRange',
    title: 'GAME_SERVER.GRID.roundIdRange',
    type: 'array',
    isList: true,
    isViewable: true,
    isSortable: false,
    isFilterable: false,
    scope: 'ALL.DEFAULT_SCHEMA_SECTIONS.dateTime',
  },
  {
    field: 'sessionIdRange',
    title: 'GAME_SERVER.GRID.sessionIdRange',
    type: 'array',
    isList: true,
    isViewable: true,
    isSortable: false,
    isFilterable: false,
    scope: 'ALL.DEFAULT_SCHEMA_SECTIONS.dateTime',
  },
  {
    field: 'createdAt',
    title: 'GAME_SERVER.GRID.createdAt',
    type: 'string',
    dataSource: '',
    isList: true,
    isViewable: true,
    isSortable: false,
    isFilterable: false,
    scope: 'ALL.DEFAULT_SCHEMA_SECTIONS.dateTime',
  },
  {
    field: 'updatedAt',
    title: 'GAME_SERVER.GRID.modifiedAt',
    type: 'string',
    isList: true,
    isViewable: true,
    isSortable: false,
    isFilterable: false,
    scope: 'ALL.DEFAULT_SCHEMA_SECTIONS.dateTime',
  },
];

export const SCHEMA_LIST = SCHEMA.filter(el => el.isList);
