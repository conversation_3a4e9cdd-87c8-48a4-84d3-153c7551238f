import { HttpClient, HttpErrorResponse, HttpParams, HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { GridDataService, SwuiNotificationsService } from '@skywind-group/lib-swui';
import { Observable, throwError } from 'rxjs';
import { catchError, share } from 'rxjs/operators';
import { API_ENDPOINT } from '../../app.constants';

import { GameServerSettings } from '../../common/typings/game-server';


const URL = `${API_ENDPOINT}/game-server/settings`;

@Injectable()
export class GameServerService implements GridDataService<GameServerSettings> {

  constructor( private notifications: SwuiNotificationsService,
               private http: HttpClient
  ) {
  }

  updateItem( id: string, data?: any ): Observable<GameServerSettings> {
    let body = JSON.stringify(data);
    const url = `${URL}/${id}`;

    return this.http.put<GameServerSettings>(url, body).pipe(
      catchError(( error: HttpErrorResponse ) => {
        this.notifications.error(error?.error?.message);
        return throwError(error);
      }),
      share()
    );
  }

  deleteItem( id: string ) {
    const url = `${URL}/${id}`;

    return this.http.delete(url).pipe(
      catchError(( error: HttpErrorResponse ) => {
        this.notifications.error(error?.error?.message);
        return throwError(error);
      }),
      share()
    );
  }

  createItem( data: GameServerSettings ): Observable<GameServerSettings> {
    let body = JSON.stringify(data);
    const url = `${URL}`;

    return this.http.post<GameServerSettings>(url, body).pipe(
      catchError(( error: HttpErrorResponse ) => {
        this.notifications.error(error?.error?.message);
        return throwError(error);
      }),
      share()
    );
  }

  getGridData( params: HttpParams ): Observable<HttpResponse<GameServerSettings[]>> {
    return this.http.get<GameServerSettings[]>(`${URL}`, {
      params,
      observe: 'response'
    });
  }

  getList(): Observable<GameServerSettings[]> {
    return this.http.get<GameServerSettings[]>(`${URL}`)
      .pipe(
        catchError(( error: HttpErrorResponse ) => {
          this.notifications.error(error?.error?.message);
          return throwError(error);
        }),
      );
  }
}
