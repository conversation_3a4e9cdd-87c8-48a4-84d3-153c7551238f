import { NgModule } from '@angular/core';
import { FlexLayoutModule } from '@angular/flex-layout';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogModule } from '@angular/material/dialog';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiGridModule, SwuiPagePanelModule } from '@skywind-group/lib-swui';
import { BoConfirmationModule } from '../../common/components/bo-confirmation/bo-confirmation.module';

import { ControlMessagesModule } from '../../common/components/control-messages/control-messages.module';
import { GameServerComponent } from './game-server.component';
import { GameServerRoutingModule } from './game-server.routing';
import { GameServerService } from './game-server.service';
import { GameServerModalComponent } from './modals/game-server-modal.component';


@NgModule({
  imports: [
    ReactiveFormsModule,
    TranslateModule,
    MatDialogModule,
    MatButtonModule,
    ControlMessagesModule,
    SwuiPagePanelModule,
    SwuiGridModule,
    GameServerRoutingModule,
    BoConfirmationModule,
    FlexLayoutModule
  ],
  declarations: [
    GameServerComponent,
    GameServerModalComponent,
  ],
  entryComponents: [
    GameServerModalComponent,
  ],
  providers: [
    GameServerService,
  ],
})
export class GameServerModule {
}
