import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

import { PERMISSIONS_LIST } from '../../app.constants';
import { GameServerComponent } from './game-server.component';


const routes: Routes = [
  {
    path: '',
    component: GameServerComponent,
    data: {
      permissions: PERMISSIONS_LIST.GAME_SERVER,
      title: 'Game Server'
    },
    children: []
  }
];

@NgModule({
  imports: [
    RouterModule.forChild(routes)
  ],
  exports: [
    RouterModule,
  ]
})
export class GameServerRoutingModule { }
