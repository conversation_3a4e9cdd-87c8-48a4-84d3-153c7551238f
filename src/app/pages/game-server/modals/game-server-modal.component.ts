import { Component, Inject, OnInit } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { ValidationService } from '../../../common/services/validation.service';

import { GameServerSettings } from '../../../common/typings/game-server';

@Component({
  selector: 'game-server-modal',
  templateUrl: './game-server-modal.component.html'
})
export class GameServerModalComponent implements OnInit {

  form: FormGroup;

  constructor( private fb: FormBuilder,
               private dialogRef: MatDialogRef<GameServerModalComponent>,
               @Inject(MAT_DIALOG_DATA) public serverSettings: GameServerSettings
  ) {
    this.form = this.fb.group({
      gameServerConfig: ['', ValidationService.IfNotEmpty(ValidationService.JSONValidator)]
    });
  }

  ngOnInit() {
    this.populateForm(this.serverSettings);
  }

  onNoClick(): void {
    this.dialogRef.close();
  }

  submit() {
    if (this.form.valid) {
      const gameServerConfig: GameServerSettings = Object.assign({}, JSON.parse(
        this.form.get('gameServerConfig').value)
      );
      this.dialogRef.close(gameServerConfig);
    }
  }

  private populateForm( data: GameServerSettings ) {
    this.form.get('gameServerConfig').setValue(JSON.stringify(data, null, 4));
  }
}
