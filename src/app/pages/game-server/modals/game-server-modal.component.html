<h1 mat-dialog-title>
  {{ (serverSettings?.name ? 'GAME_SERVER.editForm' : 'GAME_SERVER.createForm') | translate }}
</h1>
<mat-dialog-content>
  <form [formGroup]="form" fxLayout="column">
     <textarea
       formControlName="gameServerConfig"
       placeholder="{{ 'GAME_SERVER.configPlaceholder' | translate}}"
       class="form-control"
       cols="20"
       rows="20">
      </textarea>
    <control-messages
      class="validation-error-label"
      [message]="{'JSON': 'VALIDATION.JSONinvalid'}"
      [control]="form.get('gameServerConfig')">
    </control-messages>
  </form>
</mat-dialog-content>

<mat-dialog-actions align="end">
  <button mat-button color="primary" class="mat-button-md" (click)="onNoClick()">
    {{ 'DIALOG.cancel' | translate }}
  </button>
  <button mat-flat-button color="primary" class="mat-button-md" (click)="submit()">
    {{ 'DIALOG.save' | translate
    }}
  </button>
</mat-dialog-actions>
