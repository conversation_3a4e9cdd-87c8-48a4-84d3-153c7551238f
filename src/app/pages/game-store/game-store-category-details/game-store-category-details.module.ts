import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { MatCardModule } from '@angular/material/card';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiPagePanelModule } from '@skywind-group/lib-swui';
import { GameCategoryService } from '../../../common/services/game-categories.service';
import { GameStoreCategoryModule } from '../game-store-category/game-store-category.module';

import { GameStoreCategoryDetailsComponent } from './game-store-category-details.component';


@NgModule({
  imports: [
    CommonModule,
    RouterModule,
    GameStoreCategoryModule,
    TranslateModule,
    MatCardModule,
    SwuiPagePanelModule,
  ],
  exports: [GameStoreCategoryDetailsComponent],
  declarations: [GameStoreCategoryDetailsComponent],
  providers: [
    GameCategoryService,
  ],
})
export class GameStoreCategoryDetailsModule {
}
