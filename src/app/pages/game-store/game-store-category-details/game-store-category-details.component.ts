import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { combineLatest, of, Subject } from 'rxjs';
import { catchError, map, takeUntil } from 'rxjs/operators';
import { GameCategoryService } from '../../../common/services/game-categories.service';

import { GameCategory } from '../../games-categories-management/game-category.model';


@Component({
  selector: '[game-store-category-details]',
  templateUrl: 'game-store-category-details.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class GameStoreCategoryDetailsComponent implements OnInit, OnDestroy {
  loading = true;
  gameCategory: GameCategory | null;
  private readonly destroyed$ = new Subject<void>();

  constructor(
    private readonly service: GameCategoryService,
    private readonly route: ActivatedRoute,
    private readonly cdRef: ChangeDetectorRef
  ) {
  }

  ngOnInit() {
    window.scrollTo(0, 0);
    combineLatest([
      this.route.params,
      this.service.getGamestoreListWithGames()
    ]).pipe(
      map(( [{ id: categoryId }, items] ) => items.find(( { id } ) => id === categoryId)),
      catchError(() => of(null)),
      takeUntil(this.destroyed$)
    ).subscribe(gameCategory => {
      this.loading = false;
      this.gameCategory = gameCategory;
      this.cdRef.markForCheck();
    });
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }
}
