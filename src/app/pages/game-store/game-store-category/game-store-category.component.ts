import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { EntityGame } from '../../../common/typings';

import { GameCategory } from '../../games-categories-management/game-category.model';


@Component({
  selector: '[game-store-category]',
  templateUrl: 'game-store-category.component.html',
  styleUrls: ['game-store-category.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class GameStoreCategoryComponent {
  @Input('game-store-category') category: GameCategory | null;
  @Input('isCutted') isCutted: boolean = false;
  @Input('standalone') standalone: boolean = false;

  get games(): EntityGame[] {

    let games = [];

    if (this.category && Array.isArray(this.category.games)) {
      games =  this.isCutted ? this.category.games.slice(0, 6) : this.category.games;
    }

    return games;
  }
}
