import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';

import { CdnService } from '../../../common/services/cdn.service';
import { GameService } from '../../../common/services/game.service';
import { GameStoreCategoryItemModule } from './game-store-category-item/game-store-category-item.module';
import { GameStoreCategoryComponent } from './game-store-category.component';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';


@NgModule({
  imports: [
    CommonModule,
    RouterModule,
    GameStoreCategoryItemModule,
    TranslateModule,
    MatToolbarModule,
    MatIconModule,
    MatButtonModule,
  ],
  exports: [GameStoreCategoryComponent],
  declarations: [GameStoreCategoryComponent],
  providers: [
    GameService,
    CdnService,
  ],
})

export class GameStoreCategoryModule {
}
