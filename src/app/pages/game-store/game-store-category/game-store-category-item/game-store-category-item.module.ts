import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { CdnService } from '../../../../common/services/cdn.service';

import { GameStoreCategoryItemComponent } from './game-store-category-item.component';
import { MatCardModule } from '@angular/material/card';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatChipsModule } from '@angular/material/chips';


@NgModule({
  imports: [
    CommonModule,
    RouterModule,
    TranslateModule,
    MatCardModule,
    MatChipsModule,
    MatTooltipModule,
  ],
  exports: [GameStoreCategoryItemComponent],
  declarations: [GameStoreCategoryItemComponent],
  providers: [
    CdnService,
  ],
})
export class GameStoreCategoryItemModule {
}
