import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnDestroy, OnInit } from '@angular/core';
import { BehaviorSubject, iif, of, Subject } from 'rxjs';
import { mergeMap, takeUntil } from 'rxjs/operators';

import { CdnService } from '../../../../common/services/cdn.service';
import { EntityGame, Game } from '../../../../common/typings';


@Component({
  selector: '[game-store-category-item]',
  templateUrl: 'game-store-category-item.component.html',
  styleUrls: ['game-store-category-item.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class GameStoreCategoryItemComponent implements OnInit, OnDestroy {
  image: string | null;
  game: Game | null;

  @Input('game-store-category-item')
  set setItem( value: EntityGame | null ) {
    const game = value ? value.game : null;
    this.game = game;
    this.game$.next(game);
  }

  private readonly game$ = new BehaviorSubject<Game | null>(null);
  private readonly destroyed$ = new Subject<void>();

  constructor(
    private readonly cdnService: CdnService,
    private readonly cdRef: ChangeDetectorRef
  ) {
  }

  ngOnInit(): void {
    this.game$.pipe(
      mergeMap(game => iif(() => game === null, of(null), this.cdnService.getImageUrl(game.code))),
      takeUntil(this.destroyed$)
    ).subscribe(url => {
      this.image = url;
      this.cdRef.markForCheck();
    });
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }
}
