<div class="m-32">
  <mat-toolbar *ngIf="!standalone" class="bar">
    <span>{{category?.title}}</span>
    <a
      mat-flat-button
      color="primary"
      (click)="$event.preventDefault()"
      [routerLink]="['category', category?.id]"
      style="margin-left: auto">
      {{ 'MARKETING_MATERIALS.seeAll' | translate }} ({{ category?.games?.length }})
      <mat-icon fontSet="material-icons-outline">navigate_next</mat-icon>
    </a>
  </mat-toolbar>

  <div class="games-list" *ngIf="category?.games?.length">
    <div class="games-list__row" [class.cutted]="isCutted">
      <div class="games-list__item games-list__item--cut" *ngFor="let game of games" [game-store-category-item]="game">
      </div>
    </div>
  </div>
</div>




