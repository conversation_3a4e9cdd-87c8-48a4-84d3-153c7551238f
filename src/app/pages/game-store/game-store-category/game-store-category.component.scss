.games-list {

  &__heading {
    position: relative;
    margin-bottom: 20px;
  }

  &__title {
    font-size: 18px;
    font-weight: 500;
    line-height: 18px;
    text-transform: uppercase;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding-right: 100px;
  }

  &__more {
    position: absolute;
    top: 0;
    right: 0;
    font-size: 16px;
    line-height: 16px;
    color: #263238;
    i {
      margin-left: 10px;
      font-size: 14px;
    }
  }

  &__row {
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;
    margin: 0 -11px;
    overflow: hidden;
    &.cutted {
      flex-wrap: nowrap;
    }
  }

  &__item {
    box-sizing: border-box;
    padding: 11px;
    min-width: 160px;
    width: 100%;
    flex-shrink: 0;
    flex-grow: 0;
    @media screen and (min-width: 359px) {
      width: 50%
    }

    @media screen and (min-width: 768px) {
      width: calc(100% / 3)
    }

    @media screen and (min-width: 1025px) {
      width: calc(100% / 4)
    }

    @media screen and (min-width: 1281px) {
      width: calc(100% / 5);
    }
    @media screen and (min-width: 1921px) {
      width: calc(100% / 6);
    }
  }

}

.mb-30 {
  margin-bottom: 30px
}

.bar {
  padding-left: 0;
  padding-right: 0;
  background: #eaedf1;
}
