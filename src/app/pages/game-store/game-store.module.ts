import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { MatCardModule } from '@angular/material/card';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiPagePanelModule } from '@skywind-group/lib-swui';
import { GameCategoryService } from '../../common/services/game-categories.service';
import { GameStoreCategoryDetailsModule } from './game-store-category-details/game-store-category-details.module';
import { GameStoreCategoryModule } from './game-store-category/game-store-category.module';
import { GameStoreGameDetailsModule } from './game-store-game-details/game-store-game-details.module';

import { GameStoreComponent } from './game-store.component';
import { GameStoreRoutingModule } from './game-store.routing';


@NgModule({
  imports: [
    CommonModule,
    GameStoreRoutingModule,
    GameStoreCategoryModule,
    GameStoreGameDetailsModule,
    GameStoreCategoryDetailsModule,
    SwuiPagePanelModule,
    MatCardModule,
    TranslateModule,
  ],
  exports: [],
  declarations: [GameStoreComponent],
  providers: [
    GameCategoryService,
  ],
})
export class GameStoreModule {
}
