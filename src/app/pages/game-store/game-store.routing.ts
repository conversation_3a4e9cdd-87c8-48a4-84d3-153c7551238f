import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { GameStoreCategoryDetailsComponent } from './game-store-category-details/game-store-category-details.component';
import { GameStoreGameDetailsComponent } from './game-store-game-details/game-store-game-details.component';
import { GameStoreComponent } from './game-store.component';

const routes: Routes = [
  {
    path: '',
    component: GameStoreComponent,
    data: {
      title: 'Marketing Materials'
    }
  },
  {
    path: 'category/:id',
    component: GameStoreCategoryDetailsComponent,
    children: [
      {
        path: 'game-details/',
        redirectTo: '../../game-details/',
      }
    ],
    data: {
      title: 'Marketing Materials - Game Category'
    }
  },
  {
    path: 'game-details/:code',
    component: GameStoreGameDetailsComponent,
    data: {
      title: 'Marketing Materials - Game Details'
    }
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class GameStoreRoutingModule {
}
