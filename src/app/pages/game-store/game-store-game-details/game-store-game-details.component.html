<lib-swui-page-panel [title]="game?.title" backUrl="/pages/marketing-materials"></lib-swui-page-panel>


<ng-container *ngIf="loading; else contentTpl">
  <mat-card class="mat-elevation-z0 m-32">
    <i class="icon-spinner4 spinner"></i>
    <span class="ml-10">{{ 'BUSINESS_STRUCTURE.WIDGETS.loading' | translate }}</span>
  </mat-card>
</ng-container>

<ng-template #contentTpl>
  <div class="item-details">
    <div class="item-details__sidebar">
      <mat-card class="mat-elevation-z0 ">
        <mat-card-title class="item-details__title">
          {{game?.title}}
        </mat-card-title>

        <div mat-card-image class="item-details__image">
          <div class="item-details__image-wrapper">
            <img *ngIf="imageUrl" [src]="imageUrl" [alt]="game?.title"/>
          </div>
        </div>

        <mat-card-content>
          <div class="item-details__labels">
            <mat-chip-list>
              <mat-chip
                class="item-details__type"
                matTooltip="{{'Game type: ' + game?.type}}">
                {{game?.type}}
              </mat-chip>
              <mat-chip
                color="accent"
                selected
                matTooltip="{{'Game provider: ' + game?.providerTitle}}">
                {{game?.providerCode}}
              </mat-chip>
            </mat-chip-list>
          </div>

          <div *ngIf="gameInfo">
            <p>
              {{gameInfo?.shortDescriptionEN}}
            </p>
            <p>
              {{gameInfo?.descriptionEN}}
            </p>
          </div>
        </mat-card-content>
        <mat-card-actions>
          <button
            *ngIf="gameUrl"
            mat-flat-button
            color="primary"
            class="item-details__get"
            (click)="openGameModal($event)">
            {{'MARKETING_MATERIALS.GAME_DETAILS.btnGet' | translate}}
          </button>
        </mat-card-actions>
      </mat-card>
    </div>

    <mat-tab-group class="item-details__main">
      <mat-tab label="{{'MARKETING_MATERIALS.GAME_DETAILS.screenshots' | translate}}">
        <mat-card class="mat-elevation-z0">
          <div class="screens">
            <div class="screens__column" *ngFor="let rows of columns">
              <div class="screens__column-wrapper">
                <ng-container *ngFor="let screenshot of rows">
                  <a
                    class="screens__item"
                    [href]="gameInfo?.gamePath + '/' + screenshot"
                    target="_blank">
                    <img class="no-br" [src]="gameInfo?.gamePath + '/' + screenshot" alt="">
                  </a>
                </ng-container>
              </div>
            </div>
          </div>
        </mat-card>
      </mat-tab>
      <mat-tab label="{{'MARKETING_MATERIALS.GAME_DETAILS.marketing' | translate}}">
        <mat-card class="mat-elevation-z0 mb-20">
          <div class="kit">
            Marketing kit:
            <a class="kit__link" [href]="gameInfo?.gamePath + '/' + game?.code + '_marketing_kit.zip'">
              {{game?.code + '_marketing_kit.zip'}}
            </a>
            <a
              mat-icon-button
              [href]="gameInfo?.gamePath + '/' + game?.code + '_marketing_kit.zip'"
              target="_blank"
              matTooltip="{{'MARKETING_MATERIALS.GAME_DETAILS.btnDownload' | translate}}"
              class="kit__download">
              <mat-icon fontSet="material-icons-outline">save_alt</mat-icon>
            </a>
          </div>
        </mat-card>
        <mat-accordion>
            <mat-expansion-panel *ngFor="let marketingGroup of gameInfo?.marketingKit | keys">
              <mat-expansion-panel-header>
                <mat-panel-title>
                  {{marketingGroup}}
                </mat-panel-title>
                <mat-panel-description>
                  <div class="files">
                    {{'MARKETING_MATERIALS.GAME_DETAILS.files' |
                      translate: {number: gameInfo?.marketingKit[marketingGroup]?.length} }}
                  </div>
                </mat-panel-description>
              </mat-expansion-panel-header>
              <mat-list>
                <mat-list-item *ngFor="let item of gameInfo?.marketingKit[marketingGroup]" class="mark-item">
                  <mat-icon mat-list-icon fontSet="material-icons-outline">{{getIcon(item)}}</mat-icon>
                  <a mat-line [href]="gameInfo?.gamePath + '/' + item" target="_blank">{{item}}</a>
                  <a
                    mat-icon-button
                    [href]="gameInfo?.gamePath + '/' + item"
                    target="_blank"
                    style="margin-left: auto;"
                    matTooltip="{{'MARKETING_MATERIALS.GAME_DETAILS.btnDownload' | translate}}">
                    <mat-icon fontSet="material-icons-outline">save_alt</mat-icon>
                  </a>
                </mat-list-item>
              </mat-list>
            </mat-expansion-panel>
          </mat-accordion>
      </mat-tab>
    </mat-tab-group>
  </div>
</ng-template>

