import { Location } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnDestroy, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute } from '@angular/router';
import { ModalDirective } from 'ngx-bootstrap/modal';
import { combineLatest, of, Subject } from 'rxjs';
import { catchError, switchMap, takeUntil } from 'rxjs/operators';

import { CdnService, StudioGameInfo } from '../../../common/services/cdn.service';
import { GameService } from '../../../common/services/game.service';
import { GameInfo, PlayerGameURLInfo } from '../../../common/typings';
import { GameStoreGameDetailsDialogComponent } from './game-store-game-details-dialog/game-store-game-details-dialog.component';
import { toColumns } from '../../games-management/games-create/images/screenshots/screenshots.component';


const MAT_ICONS = {
  png: 'image',
  jpg: 'image',
  avi: 'movie',
  pdf: 'receipt',
  mp4: 'movie',
  ttf: 'font_download',
  otf: 'font_download',
  fnt: 'font_download',
};

@Component({
  selector: '[game-store-game-details]',
  templateUrl: './game-store-game-details.component.html',
  styleUrls: ['./game-store-game-details.component.scss'],
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class GameStoreGameDetailsComponent implements OnInit, OnDestroy {

  @ViewChild('getModal', { static: true }) public getModal: ModalDirective;

  loading = true;
  game: GameInfo | null;
  gameInfo: StudioGameInfo | null;
  gameUrl: PlayerGameURLInfo | null;
  imageUrl: string | null;
  columns: string[][] = [];

  private readonly destroyed$ = new Subject<void>();

  constructor(
    public readonly location: Location,
    private dialog: MatDialog,
    private readonly route: ActivatedRoute,
    private readonly cdnService: CdnService,
    private readonly gameService: GameService,
    private readonly cdRef: ChangeDetectorRef
  ) {
  }

  ngOnInit() {
    this.route.params.pipe(
      switchMap(( { code } ) => combineLatest([
        this.gameService.getItem(code).pipe(
          catchError(() => of(null)),
        ),
        this.cdnService.getStudioGameInfo(code).pipe(
          catchError(() => of(null)),
        ),
        this.gameService.getGameUrl(code).pipe(
          catchError(() => of(null)),
        ),
        this.cdnService.getImageUrl(code).pipe(
          catchError(() => of(null)),
        )
      ])),
      takeUntil(this.destroyed$)
    ).subscribe(( [game, gameInfo, gameUrl, imageUrl] ) => {
      this.loading = false;
      this.game = game;
      this.gameInfo = gameInfo;
      this.gameUrl = gameUrl;
      this.imageUrl = imageUrl;

      const screenshots = gameInfo && gameInfo.marketingKit ? gameInfo.marketingKit.Screenshots : [];
      this.columns = toColumns(screenshots);

      this.cdRef.markForCheck();
    });
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  getIcon( name: string ): string {
    const extension = name.split('.').pop();
    return extension in MAT_ICONS ? MAT_ICONS[extension] : 'insert_drive_file';
  }

  openGameModal( event: Event ) {
    event.preventDefault();
    this.dialog.open(GameStoreGameDetailsDialogComponent, {
      width: '700px',
      data: {
        gameUrl: this.gameUrl
      },
      disableClose: true
    });
  }
}
