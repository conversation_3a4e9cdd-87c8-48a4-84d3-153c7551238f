import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';

import { PlayerGameURLInfo } from '../../../../common/typings';


@Component({
  selector: 'game-store-game-details-dialog',
  templateUrl: './game-store-game-details-dialog.component.html'
})
export class GameStoreGameDetailsDialogComponent {
  gameUrl: PlayerGameURLInfo;

  constructor( private dialogRef: MatDialogRef<GameStoreGameDetailsDialogComponent>,
               @Inject(MAT_DIALOG_DATA) public data: { gameUrl: PlayerGameURLInfo } ) {
    this.gameUrl = data.gameUrl;
  }

  onCancel() {
    this.dialogRef.close();
  }
}
