.w-90p
  width: 90%

.fs-24
  font-size: 24px

.flex-list
  display: flex
  flex-wrap: wrap
  padding: 0
  list-style: none
  li
    margin-right: 3px

.b-cutted
  &__inner
    position: relative
    transition: max-height 0.3s cubic-bezier(0.4, 0, 1, 1)
    max-height: 999px

  &--collapse
    & ^[0]__inner
      max-height: 54px
      overflow: hidden
      transition: max-height 0.5s cubic-bezier(0.12, 0.6, 0.33, 0.98)
    &:after
      content: ''
      display: block
      height: 0
      width: 100%
      position: absolute
      left: 0
      bottom: 0
      -webkit-box-shadow: 0 10px 15px 20px #fff
      box-shadow: 0 10px 15px 20px #fff
    i
      transform: none

.screens
  &__item
    width: 100%
    margin-bottom: 20px
  &__thumb
    position: relative
    padding-top: 75%
  &__img-wrapper
    position: absolute
    left: 0
    top: 0
    width: 100%
    height: 100%
    overflow: hidden
    img
      display: block
      width: 100%

.marketing,
[panelclass="marketing"]
  border: none
  box-shadow: none

.marketing
  .panel-heading
    &.card-header
      padding: 0
  .panel-body
    &.card-block
      padding: 0 !important
      border-top: 0 !important

  &__heading
    padding-top: 10px
    padding-bottom: 10px
    border-bottom: 1px solid #ddd !important
    h6
      &.panel-title
        text-transform: uppercase

  &__item
    &:last-child
      border-bottom: none

  @media (max-width: 768px)
    &__buttons
      text-align: center
      margin-bottom: 15px

.progress-custom
  display: flex;
  align-items: center;
  &__left
    width: calc(100% - 25px);
    margin-right: 10px;
  &__right
    width: 15px;

.mark
  position: relative;
  width: 46px;
  height: 20px;
  padding: 0;
  box-sizing: border-box;
  font-size: 12px;
  line-height: 12px;
  color: #fefefe;
  text-transform: uppercase;
  text-align: center;

  &__inner
    position: absolute;
    top: 0;
    left: 0;
    z-index: 5;
    display: block;
    padding: 4px 0;
    width: 100%;
    background-color: #fafafa;

  &__collapse
    position: absolute;
    top: 0;
    left: calc(100% - 1px);
    display: inline-block;
    padding: 3px 10px;
    color: #333;
    white-space: nowrap;
    background-color: #fff;
    border: 1px solid #ddd;
    border-left: none;
    transform: translateX(-100%);
    transition: all 0.2s linear;
    opacity: 0;
    z-index: 1;
    visibility: hidden;

  &:hover
    cursor: pointer;
    & ^[0]__collapse
      display: block;
      transform: translateX(0);
      opacity: 1;
      visibility: visible;

  &--mgs
    & ^[0]__inner
      background-color: #06a93c;
    & ^[0]__collapse
      border-color: #06a93c;

  &--sw
    & ^[0]__inner
      background-color: #7fc100;
    & ^[0]__collapse
      border-color: #7fc100;

  &--bsg
    & ^[0]__inner
      background-color: #d8a501;
    & ^[0]__collapse
      border-color: #d8a501;

  &--lx
    & ^[0]__inner
      background-color: #b201d8;
    & ^[0]__collapse
      border-color: #b201d8;

.no-box-shadow
  box-shadow: none

.no-br
  border-radius: 0 !important

.mw-100
  min-width: 100px

.mw-110
  min-width: 110px

.bb-dashed
  border-bottom: 1px dashed #ddd

.inf-line
  display flex
  &__value
    margin-left: auto

