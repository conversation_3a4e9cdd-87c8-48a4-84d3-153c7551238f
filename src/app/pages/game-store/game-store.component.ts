import { ChangeDetectionStrategy, ChangeDetectorRef, Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Subject } from 'rxjs';
import { finalize, takeUntil } from 'rxjs/operators';
import { GameCategoryService } from '../../common/services/game-categories.service';

import { GameCategory } from '../games-categories-management/game-category.model';


@Component({
  selector: '[gameStore]',
  templateUrl: 'game-store.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class GameStoreComponent implements OnInit, OnDestroy {
  loading = true;
  categories: GameCategory[] = [];
  private readonly destroyed$ = new Subject<void>();

  constructor(
    private readonly service: GameCategoryService,
    private readonly cdRef: ChangeDetectorRef,
  ) {
  }

  ngOnInit() {
    this.service.getGamestoreListWithGames().pipe(
      finalize(() => {
        this.loading = false;
        this.cdRef.markForCheck();
      }),
      takeUntil(this.destroyed$)
    ).subscribe(categories => {
      this.categories = categories;
      this.cdRef.markForCheck();
    });
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }
}
