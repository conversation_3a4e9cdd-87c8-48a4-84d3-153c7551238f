<lib-swui-page-panel [title]="'MENU_SECTIONS.marketingMaterials'"></lib-swui-page-panel>

<ng-container *ngIf="loading; else contentTpl">
  <mat-card class="mat-elevation-z0 m-32">
    <i class="icon-spinner4 spinner"></i>
    <span class="ml-10">{{ 'BUSINESS_STRUCTURE.WIDGETS.loading' | translate }}</span>
  </mat-card>
</ng-container>

<ng-template #contentTpl>
    <div style="margin-bottom: 48px" [game-store-category]="category" [isCutted]="true" *ngFor="let category of categories"></div>
</ng-template>
