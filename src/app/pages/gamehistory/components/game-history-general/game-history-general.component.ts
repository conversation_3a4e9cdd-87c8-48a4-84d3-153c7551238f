import { ComponentType } from '@angular/cdk/overlay';
import { Component, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { Params } from '@angular/router';
import {
  RowAction,
  SwHubAuthService,
  SwHubEntityItem,
  SwHubEntityService,
  SwHubShortEntity,
  SwuiGridComponent,
  SwuiGridDataService,
  SwuiGridField,
  SwuiSelectOption,
  SwuiTopFilterDataService
} from '@skywind-group/lib-swui';
import moment from 'moment';
import { Subject, throwError } from 'rxjs';
import { catchError, filter, map, skip, switchMap, take, takeUntil, tap } from 'rxjs/operators';
import { EntitySettingsModel } from '../../../../common/models/entity-settings.model';
import { CsvService } from '../../../../common/services/csv.service';
import { EntityDataSourceService } from '../../../../common/services/entity-data-source.service';
import { EntitySettingsService } from '../../../../common/services/entity-settings.service';
import { GameService } from '../../../../common/services/game.service';
import { Game } from '../../../../common/typings';
import { GameHistory } from '../../../../common/typings/reports/game_history';
import { checkAndRemoveBalanceColumns } from '../internal-game-history/base-history/base-history.component';
import { IframeViewModalComponent } from '../internal-game-history/modals/iframe-view-modal/iframe-view-modal.component';
import { RoundInfoViewModalComponent } from '../internal-game-history/modals/round-info-view-modal.component';
import { GameHistoryGeneralService } from './game-history-general.service';
import { GameHistorySchema } from './schema';

const COMPONENT_NAME: string = 'game-history-general';
const REQUIRED_FILTERS = ['finished', 'playerCode', 'roundId__in'];

@Component({
  selector: 'game-history-general',
  templateUrl: './game-history-general.component.html',
  providers: [
    CsvService,
    SwuiTopFilterDataService,
    GameHistoryGeneralService,
    { provide: SwuiGridDataService, useExisting: GameHistoryGeneralService }
  ]
})
export class GameHistoryGeneralComponent implements OnInit, OnDestroy {
  readonly componentName = COMPONENT_NAME;
  schema: SwuiGridField[] = [];
  schemaFilter: SwuiGridField[] = [];
  notifyVisibility = true;
  loading: boolean = false;
  disabled: boolean = false;

  actions: RowAction[] = [];
  entityPath: string;
  initialFilterState = {
    firstTs__gte: moment().subtract(1, 'month'),
    firstTs__lt: moment()
  };
  idPathMap: SwHubEntityItem[];
  hideBalanceBeforeAndAfter = false;

  @ViewChild(SwuiGridComponent, { static: true }) grid: SwuiGridComponent<GameHistory>;

  private readonly destroy$ = new Subject<void>();

  constructor( public dialog: MatDialog,
               private historyGeneralService: GameHistoryGeneralService,
               private authService: SwHubAuthService,
               private gameService: GameService,
               private readonly filterDataService: SwuiTopFilterDataService,
               private readonly entityDataSourceService: EntityDataSourceService,
               private readonly hubEntityService: SwHubEntityService,
               private entitySettingsService: EntitySettingsService<EntitySettingsModel>,
  ) {
    hubEntityService.items$.pipe(
      takeUntil(this.destroy$)
    ).subscribe(( idPathMap ) => {
      this.idPathMap = idPathMap;
      this.initSchema();
    });
  }

  ngOnInit() {
    this.entityDataSourceService.show();

    this.hubEntityService.entitySelected$
      .pipe(
        skip(1),
        takeUntil(this.destroy$)
      )
      .subscribe(() => {
        this.filterDataService.patchFilter({ gameCode: null });
      });

    this.hubEntityService.entitySelected$.pipe(
      filter(entity => !!entity),
      switchMap(( entity: SwHubShortEntity ) => {
        return this.entitySettingsService.getSettings(entity.path)
          .pipe(
            map(( entitySettings: EntitySettingsModel ) => {
              this.hideBalanceBeforeAndAfter = entitySettings?.hideBalanceBeforeAndAfter;
              this.initSchema();

              return entity;
            }),
          );
      }),
      switchMap(( entity: SwHubShortEntity ) => {
        this.entityPath = entity.path;
        return this.gameService.getAllGames(entity.path, false, true);
      }),
      map(( games: Game[] ) => {
          this.historyGeneralService.games = games.reduce(( acc: Record<string, Game>, cur: Game ) => {
            acc[cur.code] = cur;
            return acc;
          }, {});

          return games.map<SwuiSelectOption>(( { code, title } ) => ({ id: code, text: `${title} (${code})` }));
        }
      ),
      takeUntil(this.destroy$),
    ).subscribe(( games: SwuiSelectOption[] ) => {

      this.schemaFilter = this.schemaFilter.map(( item ) => {
        if (item.field === 'gameCode' && item.type === 'select') {
          item.data = games || [];
        }
        return item;
      });
    });

    this.filterDataService.appliedFilter
      .pipe(
        takeUntil(this.destroy$)
      )
      .subscribe(params => {
        this.updateOnParamsChange(params);
      });

    this.actions.push(
      new RowAction({
        icon: 'history',
        inMenu: false,
        title: 'View details',
        canActivateFn: ( info ) => !this.disabled && info?.totalEvents > 0,
        fn: ( info ) => {
          this.hubEntityService.items$
            .pipe(
              tap(() => this.disabled = true),
              switchMap(( entities: SwHubEntityItem[] ) => {
                const entity = entities.find(( { id } ) => id === info.brandId);

                info._meta = {
                  ...info._meta,
                  name: entity.name,
                  fullPath: entity.path,
                  hideBalanceBeforeAndAfter: this.hideBalanceBeforeAndAfter,
                };
                return this.gameService.getEntityGame(info.gameCode, entity.path);
              }),
              switchMap(( data: Game ) => {
                let component: ComponentType<IframeViewModalComponent | RoundInfoViewModalComponent> = data.historyRenderType === 3 ?
                  IframeViewModalComponent :
                  RoundInfoViewModalComponent;

                return this.dialog.open(component, {
                  width: '1000px',
                  data: {
                    roundInfo: info
                  },
                  disableClose: true
                }).afterClosed();
              }),
              take(1)
            )
            .subscribe(() => this.disabled = false);
        },
      })
    );
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
    this.entityDataSourceService.hide();
  }

  initSchema() {
    const schema = new GameHistorySchema(this.idPathMap);
    this.schema = checkAndRemoveBalanceColumns(schema.schemaList, this.authService, this.hideBalanceBeforeAndAfter);
    this.schemaFilter = schema.schemaFilter;
  }

  downloadCsv() {
    this.loading = true;
    this.historyGeneralService.downloadCsv()
      .pipe(
        catchError(( err ) => {
          this.loading = false;
          return throwError(err);
        }),
        takeUntil(this.destroy$)
      ).subscribe(() => this.loading = false);
  }

  exportPage() {
    this.historyGeneralService.exportPage(this.grid.dataSource.data, this.grid.displayedColumns, this.grid.paginator.pageIndex + 1);
  }

  private updateOnParamsChange( params: Params ) {
    this.notifyVisibility = !REQUIRED_FILTERS.some(field => !!params[field]);

    if (this.notifyVisibility) {
      this.historyGeneralService.blockData();
    } else {
      this.historyGeneralService.unblockData();
    }
  }
}
