<game-notify
  *ngIf="notifyVisibility"
  message="GAMEHISTORY.INTERNAL.notify"
></game-notify>

<div class="sw-grid-layout" style="margin-top: 32px">
  <div class="sw-grid-layout__table">
    <lib-swui-schema-top-filter [schema]="schemaFilter"></lib-swui-schema-top-filter>
    <lib-swui-grid #grid
                   class="history__grid"
                   [schema]="schema"
                   [columnsManagement]="true"
                   [ignorePlainLink]="true"
                   [rowActions]="actions"
                   [rowActionsColumnTitle]="'Actions'"
                   [savedFilteredPageName]="'game-history'"
                   [savedFilteredPageParams]="initialFilterState"
                   [gridId]="componentName"
                   [useHubEntity]="true"
                   [blindPaginator]="true">
      <download-csv [loading]="loading" (downloadCsv)="downloadCsv()"></download-csv>
      <button mat-icon-button matTooltip="Export current page" (click)="exportPage()">
        <mat-icon>archive</mat-icon>
      </button>
    </lib-swui-grid>
  </div>
</div>
