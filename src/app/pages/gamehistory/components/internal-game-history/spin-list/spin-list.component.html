<div
  *ngIf="spins?.length > 1"
  class="margin-bottom12"
  mat-dialog-actions
  fxLayout="row">
  <mat-form-field appearance="outline" class="width40">
    <mat-label>Filter</mat-label>
    <input matInput trimValue (keyup)="applyFilter($event.target['value'])">
  </mat-form-field>

  <div fxLayout="row" fxFlex fxLayoutAlign="end center" class="margin-bottom12">
    <spin-list-actions [path]="path" [roundInfo]="roundInfo"></spin-list-actions>
    <button
      class="ml-10"
      color="primary"
      mat-flat-button
      (click)="downloadCsv()">
      {{ 'COMPONENTS.GRID.RESULT_DOANLOAD_CSV' | translate }}
    </button>
  </div>
</div>

<div class="mat-elevation-z0 width100">
  <table mat-table matSort [dataSource]="dataSource">

    <ng-container matColumnDef="spinId">
      <th mat-header-cell mat-sort-header *matHeaderCellDef>{{ 'GAMEHISTORY.GAME.spinId' | translate }}</th>
      <td mat-cell *matCellDef="let row">
        <ng-template #closedGameSpin>{{row.roundId}}</ng-template>
        <a *ngIf="isSpinTypeAllow(row.type); else closedGameSpin"
           (click)="handleSpinClick(row.roundId, $event)">{{row.roundId}}</a>
      </td>
    </ng-container>

    <ng-container matColumnDef="type">
      <th mat-header-cell mat-sort-header *matHeaderCellDef>{{ 'GAMEHISTORY.GAME.type' | translate }}</th>
      <td mat-cell *matCellDef="let row">{{row.type || '-'}}</td>
    </ng-container>

    <ng-container matColumnDef="bet">
      <th mat-header-cell mat-sort-header *matHeaderCellDef>{{ 'GAMEHISTORY.GAME.bet' | translate }}</th>
      <td mat-cell *matCellDef="let row">{{row.bet | formattedMoney: 2: ' ': row.currency}}</td>
    </ng-container>

    <ng-container matColumnDef="win">
      <th mat-header-cell mat-sort-header disableClear *matHeaderCellDef>{{ 'GAMEHISTORY.GAME.win' | translate }}</th>
      <td mat-cell *matCellDef="let row">{{(row.credit || row.win) | formattedMoney: 2: ' ': row.currency}}</td>
    </ng-container>

    <ng-container matColumnDef="freeBetCoin">
      <th mat-header-cell mat-sort-header *matHeaderCellDef>{{ 'PROMO.EDIT.QUALIFYING_GAMES.freebetCoinValue' | translate }}</th>
      <td mat-cell *matCellDef="let row">{{row.freeBetCoin || '-'}}</td>
    </ng-container>

    <ng-container matColumnDef="ts">
      <th mat-header-cell mat-sort-header *matHeaderCellDef>{{ 'GAMEHISTORY.GAME.date' | translate }}</th>
      <td mat-cell *matCellDef="let row">{{row.ts}}</td>
    </ng-container>

    <ng-container matColumnDef="balanceBefore">
      <th mat-header-cell mat-sort-header *matHeaderCellDef>{{ 'GAMEHISTORY.GAME.balanceBefore' | translate }}</th>
      <td mat-cell *matCellDef="let row">{{ formattingBalanceBefore(row.balanceBefore, row.currency) }}</td>
    </ng-container>

    <ng-container matColumnDef="balanceAfter">
      <th mat-header-cell mat-sort-header *matHeaderCellDef>{{ 'GAMEHISTORY.GAME.balanceAfter' | translate }}</th>
      <td mat-cell *matCellDef="let row">{{ formattingBalanceBefore(row.balanceAfter, row.currency) }}</td>
    </ng-container>

    <ng-container matColumnDef="totalJpWin">
      <th mat-header-cell mat-sort-header *matHeaderCellDef>{{ 'GAMEHISTORY.GRID.totalJpWin' | translate }}</th>
      <td mat-cell *matCellDef="let row">{{row.totalJpWin | formattedMoney: 2: ' ': row.currency}}</td>
    </ng-container>

    <ng-container matColumnDef="aamsSessionCode">
      <th mat-header-cell mat-sort-header *matHeaderCellDef>{{ 'GAMEHISTORY.GRID.aamsSessionCode' | translate }}</th>
      <td mat-cell *matCellDef="let row">{{ row?.extraData?.regulatoryData?.aamsSessionCode }}</td>
    </ng-container>

    <ng-container matColumnDef="ropCode">
      <th mat-header-cell mat-sort-header *matHeaderCellDef>{{ 'GAMEHISTORY.GRID.ropCode' | translate }}</th>
      <td mat-cell *matCellDef="let row">{{ row?.extraData?.regulatoryData?.ropCode }}</td>
    </ng-container>

    <ng-container matColumnDef="participationStartDate">
      <th mat-header-cell mat-sort-header *matHeaderCellDef>{{ 'GAMEHISTORY.GRID.participationStartDate' | translate }}</th>
      <td mat-cell *matCellDef="let row">{{ row?.extraData?.regulatoryData?.participationStartDate }}</td>
    </ng-container>

    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
    <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
</table>
  <mat-paginator [pageSizeOptions]="[5, 10, 25, 100]"></mat-paginator>
</div>
