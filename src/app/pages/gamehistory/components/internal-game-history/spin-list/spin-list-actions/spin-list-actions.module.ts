import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SpinListActionsComponent } from './spin-list-actions.component';
import { MatButtonModule } from '@angular/material/button';
import { MatMenuModule } from '@angular/material/menu';
import { MatIconModule } from '@angular/material/icon';
import { ClipboardModule } from '../../../../../../common/components/clipboard/clipboard.module';
import { TranslateModule } from '@ngx-translate/core';


@NgModule({
  declarations: [
    SpinListActionsComponent
  ],
  exports: [
    SpinListActionsComponent
  ],
  imports: [
    CommonModule,
    MatButtonModule,
    MatMenuModule,
    MatIconModule,
    ClipboardModule,
    TranslateModule,
  ]
})
export class SpinListActionsModule {
}
