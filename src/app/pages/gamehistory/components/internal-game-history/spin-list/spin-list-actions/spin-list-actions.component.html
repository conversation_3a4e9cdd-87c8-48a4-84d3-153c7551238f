<button mat-icon-button
        *ngIf="isSuperAdmin"
        [matMenuTriggerFor]="menu">
  <mat-icon>more_vert</mat-icon>
</button>
<mat-menu #menu="matMenu">
  <button mat-menu-item
          clipboard
          [disabled]="isButtonDisabled()"
          (click)="onCopyClick()">
    <span>{{ 'GAMEHISTORY.GAME.copyJsonToClipboard' | translate }}</span>
  </button>
  <button mat-menu-item
          (click)="onCopyJsonClick()">
    <span>{{ 'GAMEHISTORY.GAME.downloadDetailedCSV' | translate }}</span>
  </button>
  <button mat-menu-item
          *ngIf="(smResultAvailable$ | async) && roundInfo.finished"
          (click)="downloadSmResult()">
    <span>{{ 'COMPONENTS.GRID.SM_RESULT_DOWNLOAD' | translate }}</span>
  </button>
</mat-menu>
