import { coerceArray } from '@angular/cdk/coercion';
import { HttpErrorResponse } from '@angular/common/http';
import { Component, Input } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { SwHubAuthService, SwuiNotificationsService } from '@skywind-group/lib-swui';
import { ClipboardService } from 'ngx-clipboard';
import { Observable, Subject } from 'rxjs';
import { map, take, tap } from 'rxjs/operators';
import { FileType, simulateBrowserDownload } from '../../../../../../common/lib/files';
import { GameHistoryService } from '../../../../../../common/services/reports/gamehistory.service';
import { GameHistory } from '../../../../../../common/typings';
import { JurisdictionService } from '../../../../../../common/services/jurisdiction.service';
import { transformSpin, transformSpinDetails } from '../../../../../../common/core/currecy-transform';


function isEmptyArray( obj: Record<string, any> | Array<any> ): boolean {
  if (obj instanceof Array) {
    return obj.every(item => {
      return typeof item !== 'object';
    });
  }
  return false;
}

function flattenObj( value: Record<string, any> | Array<any>, parent: string, res = null ) {
  if (!res) {
    if (value instanceof Array) {
      return value.map(item => flattenObj(item, '', {}));
    } else {
      res = {};
    }
  }
  // tslint:disable-next-line:forin
  for (let key in value) {
    let propName = parent ? parent + '.' + key : key;
    if (typeof value[key] === 'object') {
      if (isEmptyArray(value[key])) {
        res[propName] = `"${value[key]}"`;
      } else {
        flattenObj(value[key], propName, res);
      }
    } else {
      res[propName] = `"${value[key]}"`;
    }
  }
  return res;
}

function getHeaders( value: Record<string, any> | Array<any> ): string[] {
  if (value instanceof Array) {
    return Array.from(value.reduce(( result: Set<string>, item ) => {
      const keys = Object.keys(item);
      keys.forEach(key => {
        result.add(key.toString());
      });
      return result;
    }, new Set()));
  }
  return Object.keys(value);
}

function transformToCsv( value: Record<string, any> | Array<any> ): string[] {
  const data = flattenObj(value, '');
  const headers = getHeaders(data);
  const sortedHeaders = headers.sort(( a, b ) => {
    return a.split('.').length - a.split('.').length
    || a < b ? -1 : a > b ? 1 : 0;
  });

  const result = [sortedHeaders.map(header => `"${header}"`)];
  const arr = coerceArray(data || []);
  return arr.reduce(( res, item, index ) => {
    res[index + 1] = sortedHeaders.map(header => item[header] || '').toString();
    return res;
  }, result);
}

@Component({
  selector: 'spin-list-actions',
  templateUrl: './spin-list-actions.component.html',
})
export class SpinListActionsComponent {
  @Input() roundInfo: number | any;
  @Input() path: string = '';

  loading = true;
  gridIsEmpty = true;

  readonly isSuperAdmin: boolean;
  readonly smResultAvailable$: Observable<boolean>;

  private spinListWithDetail: any[] = [];
  private readonly destroyed$ = new Subject<void>();

  constructor( private readonly service: GameHistoryService<GameHistory>,
               private readonly notifications: SwuiNotificationsService,
               authService: SwHubAuthService,
               private readonly translateService: TranslateService,
               private readonly clipboard: ClipboardService,
               jurisdictionService: JurisdictionService
  ) {
    this.isSuperAdmin = authService.isSuperAdmin;
    this.smResultAvailable$ = jurisdictionService.entityJurisdiction.pipe(map(codes => codes.includes('PT')));
  }

  ngOnInit() {
    if (this.isSuperAdmin) {
      this.getSpinListWithDetails();
    }
  }

  ngOnDestroy() {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  getSpinListWithDetails() {
    this.service.getSpinListWithDetails(this.path, this.roundInfo.roundId.toString())
      .pipe(
        tap(() => {
          this.loading = this.gridIsEmpty = true;
        }),
      ).subscribe(
      data => {
        this.spinListWithDetail = data;
        this.notifications.success(this.translateService.instant('GAMEHISTORY.GAME.notificationHistoryLoaded'));
      },
      ( error: HttpErrorResponse ) => {
        this.notifications.error(error?.error?.message);
      },
      () => {
        this.gridIsEmpty = this.spinListWithDetail.length <= 0;
        this.loading = false;
      });
  }

  onCopyClick() {
    try {
      this.clipboard.copy(JSON.stringify(this.spinListWithDetail, null, 4));
      this.notifications.success(this.translateService.instant('GAMEHISTORY.GAME.notificationRoundCopy'), '');
    } catch (exception) {
      this.notifications.error(this.translateService.instant('GAMEHISTORY.GAME.notificationCopyFailed'), '');
    }
  }

  isButtonDisabled(): boolean {
    return this.loading || this.gridIsEmpty;
  }

  onCopyJsonClick() {
    const gn = this.roundInfo.gameNameLabel;
    const rid = this.roundInfo.roundId;
    const filename = `${gn} spins list of round ${rid}(details)`;
    const data = this.spinListWithDetail.reduce((res, details) => {
      const val = JSON.parse(JSON.stringify(details));
      transformSpin(val, details.currency);
      transformSpinDetails(val, details.currency);

      res.push(val);
      return res;
    }, []);
    const transformToCsvResult = transformToCsv(data);
    simulateBrowserDownload(transformToCsvResult.join('\n'), filename, FileType.Csv);
  }

  downloadSmResult() {
    this.service.getGameHistorySmResult(this.path, this.roundInfo.roundId.toString())
      .pipe(
        take(1)
      ).subscribe(( data: string ) => {
      const { name } = this.roundInfo._meta;
      const filename = `RoundId [${this.roundInfo.roundId}] entity name [${name}]`;
      simulateBrowserDownload(data, filename, FileType.Txt);
    });
  }
}
