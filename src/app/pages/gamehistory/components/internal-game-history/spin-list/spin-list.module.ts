import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FlexModule } from '@angular/flex-layout';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatSortModule } from '@angular/material/sort';
import { MatTableModule } from '@angular/material/table';
import { TranslateModule } from '@ngx-translate/core';
import { ClipboardModule } from '../../../../../common/components/clipboard/clipboard.module';
import { TrimInputValueModule } from '../../../../../common/directives/trim-input-value/trim-input-value.module';
import { SpinListComponent } from './spin-list.component';
import { SpinListActionsModule } from './spin-list-actions/spin-list-actions.module';
import { PipesModule } from '../../../../../common/pipes/pipes.module';


@NgModule({
  declarations: [
    SpinListComponent
  ],
  exports: [
    SpinListComponent
  ],
  imports: [
    CommonModule,
    MatTableModule,
    MatSortModule,
    TranslateModule,
    MatFormFieldModule,
    ReactiveFormsModule,
    MatInputModule,
    MatButtonModule,
    MatDialogModule,
    FlexModule,
    ClipboardModule,
    MatPaginatorModule,
    MatMenuModule,
    MatIconModule,
    TrimInputValueModule,
    SpinListActionsModule,
    PipesModule
  ]
})
export class SpinListModule {
}
