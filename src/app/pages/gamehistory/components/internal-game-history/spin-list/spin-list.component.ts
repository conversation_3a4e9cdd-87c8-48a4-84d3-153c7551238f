import { Component, EventEmitter, Input, Output, ViewChild } from '@angular/core';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort, Sort } from '@angular/material/sort';
import { SortDirection } from '@angular/material/sort/sort-direction';
import { MatTableDataSource } from '@angular/material/table';
import { SwHubAuthService } from '@skywind-group/lib-swui';
import moment from 'moment';
import { Subject } from 'rxjs';
import { map, takeUntil } from 'rxjs/operators';
import { FormattedMoneyPipe } from '../../../../../common/pipes/formatted-money/formatted-money.pipe';
import { GameHistorySpinService } from '../../../../../common/services/reports/gamehistory.spin.service';
import { GameHistory, GameHistorySpin } from '../../../../../common/typings';
import { checkIfBalanceHidden } from '../base-history/base-history.component';
import { transformSpin } from '../../../../../common/core/currecy-transform';


@Component({
  selector: 'spin-list',
  templateUrl: './spin-list.component.html',
  styleUrls: ['./spin-list.component.scss'],
})
export class SpinListComponent {
  @Input() roundInfo: GameHistory;
  @Input() path: string = '';
  @Output() onShowSpin = new EventEmitter<any>();
  @Output() showSmResult = new EventEmitter<boolean>();

  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;

  displayedColumns: string[];
  dataSource?: MatTableDataSource<GameHistorySpin>;
  spins: GameHistorySpin[] = [];
  pureSpins: GameHistorySpin[] = [];
  hasFreeBet = false;

  readonly balanceHidden: boolean;

  private readonly destroyed$ = new Subject<void>();
  private readonly formattedMoneyPipe = new FormattedMoneyPipe();

  constructor( authService: SwHubAuthService, private readonly spinService: GameHistorySpinService<GameHistorySpin> ) {
    this.balanceHidden = checkIfBalanceHidden(authService);
    this.displayedColumns = this.buildDisplayedColumns();

    this.spinService.items.pipe(
      map<GameHistorySpin[], GameHistorySpin[]>(data => {
        if (Array.isArray(data) && data.length === 2) {
          if (['noMoreBets', 'rushBet'].indexOf(data[0].type) !== -1) {
            data[1].bet = data[0].bet;
            data[1].balanceBefore = data[0].balanceBefore;
            data.shift();
          }
        }
        return data.map(cur => ({
          ...cur,
          roundId: this.roundInfo.roundId + '#' + cur.spinNumber,
          ts: moment(cur.ts).format('YYYY-MM-DD HH:mm:ss')
        }));
      }),
      takeUntil(this.destroyed$)
    ).subscribe(data => {
        this.pureSpins = data;
        const spins = JSON.parse(JSON.stringify(data));
        spins.forEach(spin => {
          transformSpin(spin, spin.currency);
        });
        this.spins = spins;
        this.displayedColumns = this.buildDisplayedColumns();
        if (data.length === 1) {
          this.handleSpinClick(data[0].roundId);
        }
        this.dataSource = new MatTableDataSource(this.spins);
        this.dataSource.paginator = this.paginator;
        if (this.spins.length > 1) {
          this.showSmResult.emit(false);
        }
      }
    );
  }

  ngAfterViewInit() {
    this.sort?.sortChange.pipe(
      takeUntil(this.destroyed$),
    ).subscribe(( data: Sort ) => {
      this.paginator.pageIndex = 0;
      const { active, direction } = data;
      this.spins = this.sortTable(active, direction);
      this.dataSource.data = this.spins;
      this.dataSource.paginator = this.paginator;
    });
  }

  ngOnInit() {
    this.spinService.getList(this.roundInfo.roundId, {
      values: {
        path: this.path
      }
    });
  }

  ngOnDestroy() {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  downloadCsv() {
    this.spinService.downloadCSV(this.roundInfo, this.path, this.hasFreeBet).pipe(
      takeUntil(this.destroyed$)
    ).subscribe();
  }

  formattingBalanceBefore( valueBalanceBefore: string | number, currency: string ): string {
    return valueBalanceBefore || valueBalanceBefore === 0 ?
      this.formattedMoneyPipe.transform(valueBalanceBefore, 2, '', currency) : '-';
  }

  isSpinTypeAllow( type: string ): boolean {
    return ['revert-game', 'force-finish', 'notification', 'noMoreBets', 'finalize', 'rushBet'].indexOf(type) === -1;
  }

  handleSpinClick( roundId, event? ) {
    if (event) {
      event.preventDefault();
    }

    const item = this.pureSpins.find(round => round.roundId === roundId);

    const params = {
      roundId: this.roundInfo.roundId,
      spinNumber: item.spinNumber,
      spin: item,
      path: this.path,
    };
    this.spinService.getItem(params);
  }

  applyFilter( filterValue: string ) {
    this.dataSource.filter = filterValue.trim().toLowerCase();
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  private buildDisplayedColumns(): string[] {
    const balanceVisible = !this.balanceHidden && !(this.roundInfo?._meta?.hideBalanceBeforeAndAfter ?? false);
    const spins = this.spins;
    const regulatoryData = spins.map(item => item.extraData?.regulatoryData);

    const isBalanceBefore = balanceVisible && spins.some(item => item?.balanceBefore);
    const isBalanceAfter = balanceVisible && spins.some(item => item?.balanceAfter);
    this.hasFreeBet = spins.some(spin => spin?.type === 'freebet');
    const hasAamsSessionCode = regulatoryData.some(item => item?.aamsSessionCode);
    const hasRopCode = regulatoryData.some(item => item?.ropCode);
    const hasParticipationStartDate = regulatoryData.some(item => item?.participationStartDate);

    return [
      'spinId',
      'type',
      'bet',
      'win',
      ...(this.hasFreeBet ? ['freeBetCoin'] : []),
      'ts',
      ...(isBalanceBefore ? ['balanceBefore'] : []),
      ...(isBalanceAfter ? ['balanceAfter'] : []),
      'totalJpWin',
      ...(hasAamsSessionCode ? ['aamsSessionCode'] : []),
      ...(hasRopCode ? ['ropCode'] : []),
      ...(hasParticipationStartDate ? ['participationStartDate'] : [])
    ];
  }

  private sortTable( active: string, direction: SortDirection ) {
    active = active === 'spinId' ? 'spinNumber' : active;
    const isDate = active === 'ts';
    let isNumber = false;
    if (!isDate) {
      const value = this.spins.map(spin => spin[active]).filter(item => item !== undefined).shift();
      isNumber = typeof value === 'number';
    }
    return this.spins.sort(( first: GameHistorySpin, second: GameHistorySpin ) => {
      if (isDate) {
        if (direction === 'asc') {
          return moment(first.ts).unix() - moment(second.ts).unix();
        }
        if (direction === 'desc') {
          return moment(second.ts).unix() - moment(first.ts).unix();
        }
      }
      if (isNumber) {
        if (direction === 'asc') {
          return first[active] - second[active];
        }
        if (direction === 'desc') {
          return second[active] - first[active];
        }
      }
      if (!!direction) {
        return direction === 'asc' ? first[active]?.localeCompare(second[active]) : second[active]?.localeCompare(first[active]);
      }
    });
  }
}
