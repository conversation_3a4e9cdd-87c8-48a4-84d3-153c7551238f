import { Component, Input, OnChanges, OnInit } from '@angular/core';

@Component({
  selector: 'spin-ph-tournament',
  templateUrl: 'spin-ph-tournament.component.html',
  styleUrls: ['../spin-details/spin-tables.scss']
})
export class SpinPhTournamentComponent implements OnInit, OnChanges {

  @Input() spin: any = null;

  public phTournament: any = null;

  constructor() {
  }

  ngOnInit() {
    this.parse();
  }

  ngOnChanges() {
    this.parse();
  }

  private parse() {
    if (this.spin && ['ph-tournament-win', 'tournament'].indexOf(this.spin.type) !== -1 && this.spin.details &&
      this.spin.details.tournament && this.spin.details.tournament.feature && this.spin.details.stat) {
      this.phTournament = this.getTournamentInfo(this.spin);
    } else {
      this.phTournament = null;
    }
  }

  private getTournamentInfo( spin ) {
    return {
      name: spin.details.tournament.feature.name,
      id: spin.details.tournament.id,
      amount: spin.credit,
      currency: spin.details.currency || spin.currency,
    };
  }
}
