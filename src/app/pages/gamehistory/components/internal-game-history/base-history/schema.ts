import { Schema<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, SwuiGridField } from '@skywind-group/lib-swui';
import {
  ACCOUNT_TYPE_CLASS_MAP,
  ACCOUNT_TYPE_MAP,
  CURRENCY_LIST$,
  DEVICE_MAP,
  FINISHED_CLASS_MAP,
  FINISHED_MAP,
  FINISHED_OPTIONS_LIST,
  IS_TEST_MAP,
  OUTCOME_MAP,
  STATUS_MAP
} from '../../../../../app.constants';
import { CurrencyModel } from '../../../../../common/models/currency.model';
import { FormattedMoneyPipe } from '../../../../../common/pipes/formatted-money/formatted-money.pipe';
import { map } from 'rxjs/operators';


export const SCHEMA: SwuiGridField[] = [
  {
    field: 'path',
    title: 'GAMEHISTORY.GRID.resellerOperator',
    type: 'boAsyncChoice',
    isList: false,
    data: [],
    isViewable: false,
    isSortable: false,
    isFilterable: true,
  },
  {
    field: 'brandId',
    title: 'GAMEHISTORY.GRID.operator',
    type: 'string',
    isList: false,
    isViewable: false,
    isSortable: false,
    isFilterable: false,
  },
  {
    field: 'agentDomain',
    title: 'GAMEHISTORY.GRID.agentDomain',
    type: 'string',
    isList: false,
    isViewable: false,
    isSortable: false,
    isFilterable: false,
  },
  {
    field: 'playerCode',
    title: 'GAMEHISTORY.GRID.playerCode',
    type: 'string',
    isList: true,
    isViewable: true,
    isSortable: false,
    isFilterable: true,
    td: { type: 'user' },
    filterMatch: SchemaFilterMatchEnum.Equals,
  },
  {
    field: 'roundId',
    title: 'GAMEHISTORY.GRID.roundId',
    type: 'string',
    isList: true,
    isViewable: true,
    isSortable: false,
    isFilterable: true,
    filterMatch: SchemaFilterMatchEnum.In,
  },
  {
    field: 'contentProvider',
    title: 'GAMEHISTORY.GRID.contentProvider',
    type: 'string',
    disabled: true,
    placeholder: 'ALL.IN_PROGRESS',
    isList: false,
    isViewable: false,
    isSortable: false,
    isFilterable: true,
  },
  {
    field: 'customerCode',
    title: 'GAMEHISTORY.GRID.customerCode',
    type: 'string',
    disabled: true,
    placeholder: 'ALL.IN_PROGRESS',
    isList: false,
    isViewable: false,
    isSortable: false,
    isFilterable: false,
  },
  {
    field: 'gameNameLabel',
    title: 'GAMEHISTORY.GRID.gameNameLabel',
    type: 'string',
    isList: true,
  },
  {
    field: 'gameCode',
    title: 'GAMEHISTORY.GRID.gameCode',
    type: 'boChoice',
    data: [],
    isList: true,
    isViewable: true,
    isSortable: false,
    isFilterable: true,
    filter: {
      showSearch: true,
      title: 'GAMEHISTORY.GRID.gameNameLabel',
    },
    filterMatch: SchemaFilterMatchEnum.Equals,
    td: { type: 'string' }
  },
  {
    field: 'finished',
    title: 'GAMEHISTORY.GRID.isFinished',
    type: 'boolean',
    data: FINISHED_MAP,
    isList: true,
    isViewable: true,
    isSortable: false,
    isFilterable: true,
    td: {
      type: 'gameFinish',
      optionsList: FINISHED_OPTIONS_LIST,
      classMap: {
        true: 'label-success',
        false: 'bg-grey-300',
      },
      titleFn: ( row, schema ) => {
        const value = row[schema.field];
        return value ? 'GAMEHISTORY.GRID.isFinished' : 'GAMEHISTORY.GRID.unfinished';
      },
      classFn: ( row: any, schema: SwuiGridField ) => FINISHED_CLASS_MAP[(row && row[schema.field])]
    },
    filter: {
      field: 'finished',
      type: 'boChoice',
    }
  },
  {
    field: 'status',
    title: 'GAMEHISTORY.GRID.status',
    type: 'boolean',
    data: STATUS_MAP,
    isList: true,
    isViewable: true,
    isSortable: false,
    isFilterable: true,
    filter: {
      field: 'status',
      type: 'boChoice',
    },
    td: {
      type: 'calc',
      titleFn: ( row, schema ) => {
        if (STATUS_MAP.find(item => row[schema.field] === item.id)) {
          return STATUS_MAP.find(item => row[schema.field] === item.id).title;
        } else {
          return row[schema.field];
        }
      },
      classFn: () => '',
    }
  },
  {
    field: 'device',
    title: 'GAMEHISTORY.GRID.device',
    type: 'select',
    data: DEVICE_MAP,
    isList: true,
    isViewable: true,
    isSortable: false,
    isFilterable: true,
    filter: {
      field: 'device',
      type: 'boChoice',
    }
  },
  {
    field: 'isTest',
    title: 'GAMEHISTORY.GRID.account_type',
    type: 'select',
    data: IS_TEST_MAP,
    isList: true,
    isViewable: true,
    isSortable: false,
    isFilterable: true,
    td: {
      type: 'calc',
      titleFn: ( row: any, schema: SwuiGridField ) => ACCOUNT_TYPE_MAP[row && row[schema.field]],
      classFn: ( row: any, schema: SwuiGridField ) => ACCOUNT_TYPE_CLASS_MAP[(row && row[schema.field])],
    },
    filter: {
      field: 'isTest',
      type: 'boChoice',
    }
  },
  {
    field: 'firstTs',
    title: 'GAMEHISTORY.GRID.firstTs',
    type: 'datetimerange',
    isList: true,
    isViewable: true,
    isSortable: true,
    isFilterable: true,
    config: {
      timePicker: true,
      chooseStart: true,
    },
    filterMatch: {
      from: SchemaFilterMatchEnum.GreaterThanEquals,
      to: SchemaFilterMatchEnum.LessThan,
    },
    td: {
      type: 'timestamp',
      nowrap: true
    },
  },
  {
    field: 'ts',
    title: 'GAMEHISTORY.GRID.ts',
    type: 'datetimerange',
    isList: true,
    isViewable: true,
    isSortable: false,
    isFilterable: true,
    config: {
      timePicker: true,
      chooseStart: true,
    },
    td: {
      type: 'timestamp',
      nowrap: true
    },
    filterMatch: {
      from: SchemaFilterMatchEnum.GreaterThanEquals,
      to: SchemaFilterMatchEnum.LessThan,
    }
  },
  {
    field: 'brandId',
    title: 'GAMEHISTORY.GRID.operator',
    type: 'string',
    disabled: true,
    placeholder: 'ALL.IN_PROGRESS',
    isList: false,
    isViewable: false,
    isSortable: false,
    isFilterable: true,
    scope: 'ALL.DEFAULT_SCHEMA_SECTIONS.operatorAgent',
  },
  {
    field: 'agentDomain',
    title: 'GAMEHISTORY.GRID.agentDomain',
    type: 'string',
    disabled: true,
    placeholder: 'ALL.IN_PROGRESS',
    isList: false,
    isViewable: false,
    isSortable: false,
    isFilterable: true,
    scope: 'ALL.DEFAULT_SCHEMA_SECTIONS.operatorAgent',
  },
  {
    field: 'currency',
    title: 'GAMEHISTORY.GRID.currency',
    type: 'multiselect',
    isList: true,
    isViewable: true,
    isSortable: false,
    isFilterable: true,
    filterMatch: SchemaFilterMatchEnum.In,
    data: CURRENCY_LIST$.pipe(map(curr => curr.map((c: any) => (new CurrencyModel(c)).toSelectOption()))),
    placeholder: 'USD, EUR...',
    scope: 'ALL.DEFAULT_SCHEMA_SECTIONS.financial',
  },
  {
    field: 'balanceBefore',
    title: 'GAMEHISTORY.GRID.balanceBefore',
    type: 'numericrange',
    isList: true,
    isViewable: true,
    isSortable: false,
    isFilterable: true,
    scope: 'ALL.DEFAULT_SCHEMA_SECTIONS.financial',
    td: { type: 'currency' },
    filterMatch: {
      from: SchemaFilterMatchEnum.GreaterThanEquals,
      to: SchemaFilterMatchEnum.LessThanEquals,
    }
  },
  {
    field: 'bet',
    title: 'GAMEHISTORY.GRID.bet',
    type: 'numericrange',
    isList: true,
    isViewable: true,
    isSortable: false,
    isFilterable: true,
    filterMatch: {
      from: SchemaFilterMatchEnum.GreaterThanEquals,
      to: SchemaFilterMatchEnum.LessThanEquals,
    },
    scope: 'ALL.DEFAULT_SCHEMA_SECTIONS.financial',
    td: { type: 'currency' }
  },
  {
    field: 'win',
    title: 'GAMEHISTORY.GRID.win',
    type: 'numericrange',
    isList: true,
    isViewable: true,
    isSortable: true,
    isFilterable: true,
    filterMatch: {
      from: SchemaFilterMatchEnum.GreaterThanEquals,
      to: SchemaFilterMatchEnum.LessThanEquals,
    },
    scope: 'ALL.DEFAULT_SCHEMA_SECTIONS.financial',
    td: {
      type: 'calc',
      useTranslate: false,
      titleFn: ( row: any ) => new FormattedMoneyPipe().transform(row.credit || row.win),
      classFn: () => {
      }
    }
  },
  {
    field: 'revenue',
    title: 'GAMEHISTORY.GRID.revenue',
    type: 'numericrange',
    isList: true,
    isViewable: true,
    isSortable: false,
    isFilterable: true,
    filterMatch: {
      from: SchemaFilterMatchEnum.GreaterThanEquals,
      to: SchemaFilterMatchEnum.LessThanEquals,
    },
    scope: 'ALL.DEFAULT_SCHEMA_SECTIONS.financial',
    td: {
      type: 'calc',
      class: 'text-right',
      titleFn: ( row: any, schema: SwuiGridField ) => new FormattedMoneyPipe().transform(row[schema.field]),
      classFn: ( row: any, schema: SwuiGridField ) => ({
        'text-green': row[schema.field] > 0,
        'text-muted': row[schema.field] === '0.00',
        'text-danger': row[schema.field] < 0,
        'pull-right': true,
        'nowrap': true
      }),
    }
  },
  {
    field: 'balanceAfter',
    title: 'GAMEHISTORY.GRID.balanceAfter',
    type: 'numericrange',
    isList: true,
    isViewable: true,
    isSortable: false,
    isFilterable: true,
    scope: 'ALL.DEFAULT_SCHEMA_SECTIONS.financial',
    td: { type: 'currency' },
    filterMatch: {
      from: SchemaFilterMatchEnum.GreaterThanEquals,
      to: SchemaFilterMatchEnum.LessThanEquals,
    }
  },
  {
    field: 'outcome',
    title: 'GAMEHISTORY.GRID.outcome',
    type: 'calc',
    isList: true,
    isViewable: false,
    isSortable: false,
    isFilterable: false,
    td: {
      type: 'calc',
      titleFn: ( row, schema ) => {
        const outcome = OUTCOME_MAP.find(item => row[schema.field] === item.id);
        return outcome ? outcome.title : row[schema.field];
      },
      classFn: ( row, schema ) => {
        const outcome = OUTCOME_MAP.find(item => row[schema.field] === item.id);
        return outcome ? outcome.class : row[schema.field];
      },
    }
  },
  {
    field: 'ggrPerc',
    title: 'GAMEHISTORY.GRID.ggrPerc',
    type: 'string',
    isList: true,
    isViewable: false,
    isSortable: false,
    isFilterable: false,
    td: { type: 'percent' }
  },
  {
    field: 'totalJpWin',
    title: 'GAMEHISTORY.GRID.totalJpWin',
    type: 'number',
    isList: true,
    isViewable: false,
    isSortable: false,
    isFilterable: false,
    td: { type: 'currency' },
  },
  {
    field: 'gameContextId',
    title: 'gameContextId',
    type: 'string',
    isList: false,
    isViewable: false,
    isSortable: false,
    isFilterable: false,
  },
];


export const SCHEMA_FILTER = SCHEMA.filter(el => el.isFilterable);
export const SCHEMA_LIST = SCHEMA.filter(el => el.isList);
