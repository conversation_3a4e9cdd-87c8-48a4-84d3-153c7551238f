import { SwHubAuthService, SwuiGridField } from '@skywind-group/lib-swui';
import { PERMISSIONS_NAMES } from '../../../../../app.constants';

export function checkIfBalanceHidden( authService: SwHubAuthService ): boolean {
  return authService.allowedTo([
    PERMISSIONS_NAMES.DISABLE_ENTITY_GAME_HISTORY_BALANCES,
    PERMISSIONS_NAMES.DISABLE_KEYENTITY_GAME_HISTORY_BALANCES,
  ]) && authService.isSuperAdmin === false;
}

export function checkAndRemoveBalanceColumns( items: SwuiGridField[], authService: SwHubAuthService,
                                              hideBalanceBeforeAndAfter = false
): SwuiGridField[] {
  if (checkIfBalanceHidden(authService) || hideBalanceBeforeAndAfter) {
    const balanceFields = ['balanceBefore', 'balanceAfter'];
    balanceFields.forEach(( fieldName ) => {
      const idx = items.findIndex(i => i.field === fieldName);

      if (idx > -1) {
        items.splice(idx, 1);
      }
    });
  }

  return items;
}
