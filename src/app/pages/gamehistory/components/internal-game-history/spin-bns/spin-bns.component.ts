import { Component, Input } from '@angular/core';

@Component({
  selector: 'spin-bns',
  templateUrl: './spin-bns.component.html',
  styleUrls: ['../spin-details/spin-tables.scss']
})
export class SpinBnsComponent {
  @Input() spin: any = null;

  bns: any = null;

  constructor() {}

  ngOnInit() {
    this.parse();
  }

  ngOnChanges() {
    this.parse();
  }

  public isMaximumRedemption() {
    return this.spin && this.spin.details && (this.spin.details.amount > this.spin.details.redeemedAmount);
  }

  private parse(): void {
    const type = this.spin && this.spin.type;

    if (type === 'redeem-bns') {
      this.bns = this.getBnsInfo(this.spin);
    } else {
      this.bns = null;
    }
  }

  private getBnsInfo(spin) {
    const bns = {
      coins: spin.details.amount,
      redeemedAmount: spin.details.redeemedAmount,
      amount: spin.details.redeemBalance,
      currency: spin.details.redeemCurrency,
    };

    return bns;
  }
}
