<ng-template [ngIf]="bns">
  <div class="tableRedeem">
    <h5 translate="">GAMEHISTORY.GAME.bonusCounsRedeems</h5>
    <table class="table">
      <tr *ngIf="bns.coins">
        <th translate>GAMEHISTORY.GAME.coinRedeems</th>
        <td *ngIf="!isMaximumRedemption()">BNS {{bns.coins | formattedNumber}}</td>
        <td *ngIf="isMaximumRedemption()">BNS {{bns.redeemedAmount | formattedNumber}}
          <span>({{'GAMEHISTORY.GAME.capped'| translate}})</span>
        </td>
      </tr>

      <tr *ngIf="bns.amount">
        <th translate>GAMEHISTORY.GAME.amount</th>
        <td>{{bns.currency}} {{bns.amount | formattedNumber}}</td>
      </tr>
    </table>
  </div>
</ng-template>

