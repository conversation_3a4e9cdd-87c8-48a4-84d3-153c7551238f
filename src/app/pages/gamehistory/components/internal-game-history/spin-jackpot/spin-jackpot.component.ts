import { Component, Input, OnChanges, OnInit } from '@angular/core';
import { L7JP_POOLS } from '../spin-details/spin-details.component';

@Component({
  selector: 'spin-jackpot',
  templateUrl: './spin-jackpot.component.html',
  styleUrls: ['../spin-details/spin-tables.scss']
})
export class SpinJackpotComponent implements OnInit, OnChanges {
  @Input() spin: any = null;

  multipleJackpot: any[] = null;
  jackpot: any = null;
  jackpotSrtChallenge: any = null;
  jackpotSrtTournament: any = null;
  jackpotPool = '';

  ngOnInit() {
    this.initJackpot();
  }

  ngOnChanges() {
    this.initJackpot();
  }

  initJackpot() {
    this.parse();

    if (this.isJackpotSrtTournament()) {
      this.setJackpotSrtTournament();
    }

    if (this.isJackpotSrtChallenge()) {
      this.setJackpotSrtChallenge();
    }

    this.jackpotPool = this.jackpot?.pool && this.jackpot.pool.includes('sw_suli') ? 'sw_suli.main' : this.jackpot?.pool;
  }

  public isJackpotSrtChallenge() {
    return this.spin && this.spin.details && Array.isArray(this.spin.details.result) &&
      this.spin.details.result[0].event === 'win' &&
      this.spin.details.result[0].pool === 'prize_pool';
  }

  public isJackpotSrtTournament() {
    return this.spin && this.spin.details && Array.isArray(this.spin.details.result) &&
      this.spin.details.result[0].event === 'win' &&
      (this.spin.details.result[0].pool === 'pool0' ||
        this.spin.details.result[0].pool === 'pool1' ||
        this.spin.details.result[0].pool === 'pool2' ||
        this.spin.details.result[0].pool === 'pool3'
      );
  }

  public isMustWinJackpot() {
    return this.spin && this.spin.details && this.spin.details.result && Array.isArray(this.spin.details.result) &&
      this.spin.details.result.some(item => item.jackpotType && item.jackpotType.indexOf('ph-must-win') !== -1);
  }

  public isL7Jackpot(): boolean {
    return this.spin && this.spin.details && Array.isArray(this.spin.details.result) &&
      this.spin.details.result[0].event === 'win' && this.spin.details.result[0].jackpotType === 'sw-instant-jp' &&
      !!L7JP_POOLS[this.spin.details.result[0].pool];
  }

  private parse(): void {
    const type = this.spin && this.spin.type;

    if (type === 'jackpot-mini-game') {
      this.jackpot = this.getJackpotMiniGame();
    } else if (type === 'jackpot-win') {
      this.jackpot = this.getJackpotWin();
    } else if (this.spin && this.spin.details && this.spin.details.roundInfoFinalizeReels) {
      this.jackpot = this.getJackpotFinalizeReels();
    } else {
      this.jackpot = null;
    }

    if (this.spin && this.spin.details && this.spin.details.result && Array.isArray(this.spin.details.result)
      && this.spin.details.result.length > 1) {
      this.parseMultipleJackpot();
    } else {
      this.multipleJackpot = null;
    }
  }

  private getJackpotWin(): any {
    const details = this.spin && this.spin.details;
    const jackpot: any = {};

    if (details.result && details.result[0]) {
      const result = details.result[0];
      const transactionId = result.transactionId;
      const currency = this.spin.currency || details.currency;
      const amount = result.amount;
      const title = result.title;

      jackpot.transactionId = transactionId;
      jackpot.pool = this.getPool(result.pool);
      jackpot.currency = currency;
      jackpot.amount = amount;
      jackpot.title = title;
    }

    return jackpot;
  }

  private getJackpotMiniGame(): any {
    const details = this.spin && this.spin.details;
    const jackpot: any = {};

    if (details.roundsInfo && details.roundsInfo.rounds && details.roundsInfo.currentRoundId >= 0) {
      const round = details.roundsInfo.rounds.find(r => r.id === details.roundsInfo.currentRoundId);
      if (round) {
        const result = round.items[round.selectedIds];
        if (result) {
          jackpot.pool = this.getPool(result.visibleValue);
          jackpot.title = result.title;
        }
      }
    }

    if (details.totalWin) {
      jackpot.amount = details.totalWin;
    }

    jackpot.currency = this.spin.currency || details.currency;

    return jackpot;
  }

  private getJackpotFinalizeReels(): any {
    const details = this.spin && this.spin.details;
    const jackpot: any = {};

    if (details.roundInfoFinalizeReels && details.roundInfoFinalizeReels.rounds &&
      details.roundInfoFinalizeReels.currentRoundId >= 0) {
      const round = details.roundInfoFinalizeReels.rounds[details.roundInfoFinalizeReels.currentRoundId];
      if (round) {
        const result = round.items[round.selectedIds];
        if (result) {
          jackpot.pool = this.getPool(result.visibleValue);
        }
      }

      if (details.roundInfoFinalizeReels.totalWin) {
        jackpot.amount = details.roundInfoFinalizeReels.totalWin;
      }

      return jackpot;
    }
  }

  private getPool( pool: string ): string {
    if (this.spin.gameId) {
      return `${this.spin.gameId}.${pool}`;
    } else {
      return `${this.spin.gameCode}.${pool}`;
    }
  }

  private setJackpotSrtChallenge() {
    if (this.spin && this.spin.details && Array.isArray(this.spin.details.result)) {
      this.jackpotSrtChallenge = this.spin.details.result[0];
    }
  }

  private setJackpotSrtTournament() {
    if (this.spin && this.spin.details && Array.isArray(this.spin.details.result)) {
      this.jackpotSrtTournament = this.spin.details.result[0];
    }
  }

  private parseMultipleJackpot() {
    this.multipleJackpot = this.spin.details.result;
  }
}
