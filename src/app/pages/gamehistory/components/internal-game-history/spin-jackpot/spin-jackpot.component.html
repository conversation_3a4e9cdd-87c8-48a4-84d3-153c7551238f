<ng-template
  [ngIf]="jackpot && !isJackpotSrtTournament() && !isJackpotSrtChallenge() && !multipleJackpot">
  <div [ngClass]="{ 'tableMustWinJackpot': isMustWinJackpot() }">
    <h5 *ngIf="!spin?.isBonus && !spin?.details?.roundInfoFinalizeReels" translate="">GAMEHISTORY.GAME.jackpot</h5>

    <table class="table">
      <tbody>
      <tr *ngIf="jackpot.transactionId && !isL7Jackpot()">
        <th translate>GAMEHISTORY.GAME.transactionID</th>
        <td>{{jackpot.transactionId}}</td>
      </tr>

      <tr *ngIf="jackpot.pool && !isL7Jackpot()">
        <th class="col-md-6" *ngIf="!spin?.isBonus  && !spin?.details?.roundInfoFinalizeReels; else bonusTitle"
            translate>
          GAMEHISTORY.GAME.pool
        </th>
        <td>
          {{ isMustWinJackpot() && jackpot?.title ?
          jackpot?.title :
          ('GAMEHISTORY.GAME.POOL.' + jackpotPool | translate) }}
        </td>
      </tr>

      <tr>
        <th class="col-md-6" translate>GAMEHISTORY.GAME.amount</th>
        <td>{{jackpot?.currency}} {{jackpot?.amount | formattedNumber}}</td>
      </tr>
      </tbody>
    </table>
  </div>
</ng-template>

<ng-template #bonusTitle>
  <th class="col-md-6" translate>GAMEHISTORY.GAME.bonus</th>
</ng-template>

<ng-template [ngIf]="isJackpotSrtChallenge()">
  <div class="tableSrt">
    <h5 translate>GAMEHISTORY.GAME.jackpot</h5>
    <table class="table">
      <tbody>
      <tr *ngIf="jackpotSrtChallenge?.pool">
        <th translate>GAMEHISTORY.GAME.pool</th>
        <td translate>GAMEHISTORY.GAME.SRT.challengeWinPool</td>
      </tr>
      <tr>
        <th translate>GAMEHISTORY.GAME.amount</th>
        <td>{{jackpotSrtChallenge?.amount | formattedNumber}}</td>
      </tr>
      </tbody>
    </table>
  </div>
</ng-template>

<ng-template [ngIf]="isJackpotSrtTournament()">
  <div class="tableSrt">
    <h5 translate="">GAMEHISTORY.GAME.jackpot</h5>
    <table class="table">
      <tbody>
      <tr *ngIf="jackpotSrtTournament?.pool">
        <th translate>GAMEHISTORY.GAME.pool</th>
        <td translate>GAMEHISTORY.GAME.SRT.tournamentWinPool.{{jackpotSrtTournament?.pool}}</td>
      </tr>
      <tr>
        <th translate>GAMEHISTORY.GAME.amount</th>
        <td>{{jackpotSrtTournament?.amount | formattedNumber}}</td>
      </tr>
      </tbody>
    </table>
  </div>
</ng-template>

<ng-template
  [ngIf]="jackpot && !isJackpotSrtTournament() && !isJackpotSrtChallenge() && multipleJackpot">
  <div *ngFor="let jackpotItem of multipleJackpot; let i = index"
       [ngClass]="{ 'tableMustWinJackpot': isMustWinJackpot() }">
    <h5 translate>GAMEHISTORY.GAME.jackpot <span>#{{i + 1}}</span></h5>

    <table class="table" style="border-collapse: unset;">
      <tbody>
      <tr *ngIf="jackpotItem?.transactionId">
        <th translate>GAMEHISTORY.GAME.transactionID</th>
        <td>{{jackpotItem?.transactionId}}</td>
      </tr>
      <tr *ngIf="jackpotItem?.pool">
        <th class="col-md-6" translate>GAMEHISTORY.GAME.pool</th>
        <td>
          {{ isMustWinJackpot() && jackpotItem?.title ?
          jackpotItem?.title :
          ('GAMEHISTORY.GAME.POOL.' + getPool(jackpotItem?.pool) | translate) }}
        </td>
      </tr>
      <tr>
        <th class="col-md-6" translate>GAMEHISTORY.GAME.amount</th>
        <td>{{jackpotItem?.currency || jackpot?.currency}} {{jackpotItem?.amount | formattedNumber}}</td>
      </tr>
      </tbody>
    </table>
  </div>
</ng-template>
