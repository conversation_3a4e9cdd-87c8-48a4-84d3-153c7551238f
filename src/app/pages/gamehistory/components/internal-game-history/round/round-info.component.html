<div *ngIf="roundInfo" class="spins-amount">
  <div class="sw-chip sw-chip-blue spins-amount__item" *ngIf="roundInfo.freeSpinsCountWon + roundInfo.freeSpinsCountNotWon">
    {{'GAMEHISTORY.freeSpinsWon' | translate: {number: roundInfo.freeSpinsCountWon + roundInfo.freeSpinsCountNotWon} }}
  </div>
  <div class="sw-chip spins-amount__item" *ngIf="roundInfo.freeSpinsCountNotWon">
    {{'GAMEHISTORY.freeSpinsRemaining' | translate: {number: roundInfo.freeSpinsCountNotWon} }}
  </div>
</div>

<spin-list
  [hidden]="activeSpin"
  [roundInfo]="roundInfo"
  [path]="path"
  (onShowSpin)="showSpin($event)"
  (showSmResult)="showSmResultEmitter($event)"
  #spinList>
</spin-list>

<spin-details
  *ngIf="activeSpin"
  [activeSpin]="activeSpin"
  [roundInfo]="roundInfo">
</spin-details>
