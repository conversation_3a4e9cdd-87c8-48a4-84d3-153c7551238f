import { MissingTranslationHandler, MissingTranslationHandlerParams } from '@ngx-translate/core';

export class SWMissingTranslationPoolHandler extends MissingTranslationHandler {
  handle( params: MissingTranslationHandlerParams ) {
    if (params.key.startsWith('GAMEHISTORY.GAME.POOL')) {
      const fallbackKey = params.key.replace(/(GAMEHISTORY\.GAME\.POOL)\.(.*?)\.(.*)/, '$1.$3');
      return params.translateService.get(fallbackKey);
    }

    return params.key;
  }
}
