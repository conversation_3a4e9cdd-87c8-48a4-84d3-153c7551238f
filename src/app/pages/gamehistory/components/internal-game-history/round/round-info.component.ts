import { Component, EventEmitter, Input, Output } from '@angular/core';

import { GameHistorySpinService } from '../../../../../common/services/reports/gamehistory.spin.service';
import { GameHistory, GameHistorySpin } from '../../../../../common/typings';

@Component({
  selector: 'round-info',
  templateUrl: './round-info.component.html',
  styleUrls: ['./round-info.component.scss'],
})
export class RoundInfoComponent {
  @Input() roundInfo: GameHistory;
  @Input() path: string = '';
  @Output() showSmResult = new EventEmitter<boolean>();
  public loading = true;
  public activeSpin;

  private subscriptions: any[] = [];

  constructor( public spinService: GameHistorySpinService<GameHistorySpin> ) {
    let spinSubscription = this.spinService.item
      .subscribe(( data: GameHistorySpin ) => {
        this.activeSpin = data;
      });
    this.subscriptions.push(spinSubscription);
  }

  ngOnInit() {
  }

  ngOnChanges() {
    this.spinService._item.next();
  }

  ngOnDestroy() {
    this.subscriptions.forEach(subscription => subscription.unsubscribe());
  }

  public showSpin( event ) {
    console.log('showSpin', event);
  }

  showSmResultEmitter( event ) {
    this.showSmResult.emit(event);
  }
}
