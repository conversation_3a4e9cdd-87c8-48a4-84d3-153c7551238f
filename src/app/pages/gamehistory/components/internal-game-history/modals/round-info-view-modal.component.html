<ng-container [ngSwitch]="!!roundInfo?._meta">
  <ng-container *ngSwitchCase="true">
    <div class="info-header">
      <div class="info-header__part-1">
        <div class="info-header__logo">
          <img [src]="noImage" alt>
        </div>
        <div class="info-header__body">
          <div class="info-header__row">
          <span class="info-header__title">
              <span>{{roundInfo.gameNameLabel}}</span>
              <span>({{roundInfo.gameCode}})</span>
          </span>
            <span class="info-header__chip sw-chip" [ngClass]="{'sw-chip-green': roundInfo.finished}">
              {{(roundInfo.finished ? 'GAMEHISTORY.GRID.isFinished' : 'GAMEHISTORY.GRID.unfinished') | translate}}
            </span>
            <span *ngIf="roundInfo.isTest" class="info-header__chip sw-chip">{{'GAMEHISTORY.GRID.isTest' | translate}}</span>
          </div>
          <div class="info-header__row">
            <div class="info-header__item">
              <span class="info-header__label">{{'GAMEHISTORY.GRID.roundId' | translate}}:</span>
              <span class="info-header__value">{{roundInfo.roundId}}</span>
            </div>
            <div class="info-header__item" *ngIf="roundInfo?.extraData?.extRoundId">
              <span class="info-header__label">{{ 'GAMEHISTORY.GRID.extRoundId' | translate }}:</span>
              <span class="info-header__value">{{ roundInfo?.extraData?.extRoundId }}</span>
            </div>
            <div class="info-header__item">
              <span class="info-header__label">{{'GAMEHISTORY.GRID.playerCode' | translate}}:</span>
              <span class="info-header__value">{{roundInfo.playerCode}}</span>
            </div>
          </div>
        </div>
      </div>
      <div class="info-header__part-2">
        <button *ngIf="roundInfo.finished && isSuperAdmin && showSmResult && (smResultAvailable$ | async)"
                mat-flat-button
                color="primary"
                (click)="downloadSmResult()">
          {{ 'COMPONENTS.GRID.SM_RESULT_DOWNLOAD' | translate }}
        </button>
      </div>
    </div>

    <div mat-dialog-content>
      <round-info [roundInfo]="roundInfo" [path]="path" (showSmResult)="showSmResultEmitter($event)"></round-info>
    </div>
  </ng-container>
  <ng-container *ngSwitchCase="false">
    <div mat-dialog-content>
      <div class="alert-message">
        <div class="alert-message__icon">
          <mat-icon fontSet="material-icons-outline">error_outline</mat-icon>
        </div>
        <div class="alert-message__message">
          Game details for <b>Round ID {{roundInfo.roundId}}</b> are temporary unavailable.<br/>
          Please contact Support Team via <a class="link" (click)="sendEmail()"><EMAIL></a> to retrieve round
          details.
        </div>
      </div>
    </div>
  </ng-container>
</ng-container>
<mat-dialog-actions align="end">
  <ng-container *ngIf="spinItemsResponse?.length > 1 && spinItem !== undefined">
    <button
      *ngIf="spinItem?.hasNext || spinItem?.hasPrev"
      mat-stroked-button
      (click)="footerBack()">
      {{ 'DIALOG.back' | translate }}
    </button>

    <button
      *ngIf="spinItem?.hasPrev && isSpinTypeAllow(spinItem?.prevType)"
      mat-stroked-button
      (click)="footerPrev()">
      {{ 'DIALOG.previous' | translate }}
    </button>

    <button
      *ngIf="spinItem?.hasNext && isSpinTypeAllow(spinItem?.nextType)"
      mat-flat-button
      color="primary"
      (click)="footerNext()">
      {{ 'DIALOG.next' | translate }}
    </button>
  </ng-container>

  <button mat-button
          color="primary"
          class="mat-button-md"
          mat-dialog-close>
    {{ 'DIALOG.close' | translate }}
  </button>
</mat-dialog-actions>
