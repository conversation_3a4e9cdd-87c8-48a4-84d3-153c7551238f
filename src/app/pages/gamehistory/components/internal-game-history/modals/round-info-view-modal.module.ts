import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FlexLayoutModule, FlexModule } from '@angular/flex-layout';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { GameHistorySpinService } from '../../../../../common/services/reports/gamehistory.spin.service';

import { RoundInfoModule } from '../round/round-info-module';
import { RoundInfoViewModalComponent } from './round-info-view-modal.component';
import { MatDialogModule } from '@angular/material/dialog';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';

@NgModule({
  imports: [
    CommonModule,
    RoundInfoModule,
    MatCardModule,
    FlexModule,
    TranslateModule,
    MatButtonModule,
    MatDialogModule,
    MatIconModule,
    FlexLayoutModule,
  ],
  exports: [RoundInfoViewModalComponent],
  declarations: [RoundInfoViewModalComponent],
  providers: [GameHistorySpinService],
})
export class RoundInfoViewModalModule {
}
