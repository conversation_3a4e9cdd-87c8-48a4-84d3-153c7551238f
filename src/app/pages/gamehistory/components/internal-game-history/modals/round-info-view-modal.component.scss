.info-header {
  width: 100%;
  display: flex;

  &__part-1,
  &__part-2 {
    width: 50%;
    display: flex;
    align-items: center;
  }

  &__part-2 {
    justify-content: end;
    padding-right: 1em;
    align-items: flex-start;
  }

  &__logo {
    width: 50px;
    height: 50px;

    img {
      display: block;
      width: 100%;
      height: auto;
    }
  }

  &__body {
    padding-left: 24px;
  }

  &__title {
    font-size: 16px;
    font-weight: 500;
  }

  &__title {
    margin-right: 10px;
  }

  &__chip {
    margin-right: 10px;
  }

  &__item {
    display: flex;
    font-size: 14px;
    margin-right: 10px;
  }

  &__label {
    display: block;
    margin-right: 4px;
    font-weight: 500;
  }

  &__row {
    display: flex;
  }
}

.alert-message {
  display: flex;
  padding: 6px 16px;
  color: rgb(102, 60, 0);
  background-color: rgb(255, 244, 229);

  &__icon {
    display: flex;
    opacity: 0.9;
    padding: 11px 0;
    margin-right: 12px;
    color: #ff9800;

    mat-icon {
      font-size: 44px;
      width: 44px;
      height: 44px;
      line-height: 44px;
    }
  }

  &__message {
    padding: 8px 0;
  }
}

mat-dialog-actions {
  button {
    margin-left: 8px;

    &:first-child {
      margin-left: 0;
    }

    &.close-button {
      margin-left: auto;
    }
  }
}
