import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogModule } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { TranslateModule } from '@ngx-translate/core';
import { IframeViewModalComponent } from './iframe-view-modal.component';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';

@NgModule({
  imports: [
    MatDialogModule,
    MatButtonModule,
    TranslateModule,
    CommonModule,
    MatMenuModule,
    MatIconModule,
    MatProgressSpinnerModule
  ],
  exports: [
    IframeViewModalComponent,
  ],
  declarations: [
    IframeViewModalComponent,
  ],
  providers: [],
})
export class IframeViewModalModule {
}
