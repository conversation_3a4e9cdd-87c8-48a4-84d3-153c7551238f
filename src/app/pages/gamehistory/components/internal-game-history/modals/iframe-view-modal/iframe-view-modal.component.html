<mat-dialog-actions class="iframe-view-modal iframe-view-modal_top">
  <button *ngIf="roundInfo.finished && isSuperAdmin && (smResultAvailable$ | async)"
          mat-flat-button
          color="primary"
          (click)="downloadSmResult()">
    {{ 'COMPONENTS.GRID.SM_RESULT_DOWNLOAD' | translate }}
  </button>
  <button mat-flat-button
          color="primary"
          (click)="downloadCsv()">
    {{ 'COMPONENTS.GRID.RESULT_DOANLOAD_CSV' | translate }}
  </button>
  <button *ngIf="isSuperAdmin && !selectedSpinId"
          mat-flat-button
          color="primary"
          [disabled]="!spinListWithDetail"
          (click)="onCopyRoundClick()">
    <ng-container *ngIf="!spinListWithDetail; else title">
      <div>
        <i class="icon-spinner4 spinner"></i>
      </div>
    </ng-container>
    <ng-template #title>
      {{ ('GAMEHISTORY.GAME.copyJsonToClipboard' | translate) + ' (' + ('GAMEHISTORY.GAME.round' | translate) + ')' }}
    </ng-template>
  </button>
  <button *ngIf="isSuperAdmin && selectedSpinId"
          mat-flat-button
          color="primary"
          [disabled]="spinLoading"
          (click)="onCopySpinClick()">
    <ng-container *ngIf="spinLoading; else title">
      <div>
        <i class="icon-spinner4 spinner"></i>
      </div>
    </ng-container>
    <ng-template #title>
      {{ ('GAMEHISTORY.GAME.copyJsonToClipboard' | translate) + ' (' + ('GAMEHISTORY.GAME.spin' | translate) + ')' }}
    </ng-template>
  </button>
</mat-dialog-actions>

<div mat-dialog-content class="mb-10" style="position: relative">
  <div id="frame-container" [style.visibility]="iframeLoading ? 'hidden' : 'visible'">
    <iframe #iframe></iframe>
  </div>

  <div class="spinner-wrapper" *ngIf="iframeLoading">
    <mat-spinner [diameter]="40"></mat-spinner>
  </div>
</div>

<mat-dialog-actions class="iframe-view-modal">
  <button mat-button
          color="primary"
          class="mat-button-md"
          (click)="closeModal()">
    {{ 'DIALOG.close' | translate }}
  </button>
</mat-dialog-actions>
