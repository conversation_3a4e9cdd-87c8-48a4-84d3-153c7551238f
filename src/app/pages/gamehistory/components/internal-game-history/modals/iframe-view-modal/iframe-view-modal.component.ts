import { HttpErrorResponse } from '@angular/common/http';
import { Component, ElementRef, Inject, ViewChild } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { TranslateService } from '@ngx-translate/core';
import { SwHubAuthService, SwuiNotificationsService } from '@skywind-group/lib-swui';
import { ClipboardService } from 'ngx-clipboard';
import { Observable } from 'rxjs';
import { map, take, tap } from 'rxjs/operators';
import { FileType, simulateBrowserDownload } from '../../../../../../common/lib/files';
import { JurisdictionService } from '../../../../../../common/services/jurisdiction.service';
import { GameHistoryService } from '../../../../../../common/services/reports/gamehistory.service';
import { GameHistorySpinService } from '../../../../../../common/services/reports/gamehistory.spin.service';
import { GameHistory, GameHistorySpin } from '../../../../../../common/typings';
import { CsvSchema, CsvService } from '../../../../../../common/services/csv.service';
import { transformSpin } from '../../../../../../common/core/currecy-transform';

export interface GameHistoryModalInfo {
  roundInfo: GameHistory;
  isSuperAdmin: boolean;
  accessToken: string;
}

@Component({
  selector: 'iframe-view-modal',
  templateUrl: './iframe-view-modal.component.html',
  styleUrls: ['./iframe-view-modal.component.scss'],
})
export class IframeViewModalComponent {

  @ViewChild('iframe', { static: true }) protected iframe: ElementRef;

  roundInfo: GameHistory;
  path: string = '';

  public spinListWithDetail: any[];

  public spinLoading: boolean;
  public selectedSpinId: string;

  isSuperAdmin: boolean;
  readonly smResultAvailable$: Observable<boolean>;
  iframeLoading = true;

  constructor( @Inject(MAT_DIALOG_DATA) data: GameHistoryModalInfo,
               public dialogRef: MatDialogRef<IframeViewModalComponent>,
               private translateService: TranslateService,
               public spinService: GameHistorySpinService<GameHistorySpin>,
               public roundService: GameHistoryService<GameHistory>,
               public notifications: SwuiNotificationsService,
               private csvService: CsvService,
               private clipboard: ClipboardService,
               private authService: SwHubAuthService,
               jurisdictionService: JurisdictionService
  ) {
    this.smResultAvailable$ = jurisdictionService.entityJurisdiction.pipe(map(codes => codes.includes('PT')));
    this.isSuperAdmin = this.authService.isSuperAdmin;
    this.roundInfo = data.roundInfo;
    const { fullPath } = data.roundInfo._meta;
    this.path = fullPath && fullPath !== ':' ? fullPath : '';
  }

  ngOnInit() {
    this.getSpinListWithDetails();
  }

  closeModal() {
    this.dialogRef.close();
  }

  initIFrame( appUrl: string ) {
    const iframe = this.iframe.nativeElement;

    iframe.src = `${appUrl}?mode=bo&round_id=${this.roundInfo.roundId}&`
      + `gameName=${this.roundInfo.gameNameLabel}&currency=${this.roundInfo.currency}&url=${location.origin}/v1`;
    iframe.width = '100%';
    iframe.height = '700px';
    iframe.setAttribute('class', 'no-borders');

    document.getElementById('frame-container').appendChild(iframe);

    window.addEventListener('message', ( e ) => {
        let data;

        try {
          data = JSON.parse(e.data);
        } catch (e) {
        }

        if (data) {
          if (data.ready) {
            this.sendMessage(JSON.stringify({
              accessToken: this.authService.accessToken,
              path: this.path,
            }), appUrl);

            this.iframeLoading = false;
          }

          if (data.msgId === 'gh2boSpinNumber') {
            this.selectedSpinId = data.value.toString();
          }

          if (data.msgId === 'gh2boOperationsList') {
            this.selectedSpinId = null;
          }
        }
      }
    );
  }

  sendMessage( msg, targetOrigin ) {
    if (this.iframe && this.iframe.nativeElement && this.iframe.nativeElement.contentWindow) {
      this.iframe.nativeElement.contentWindow.postMessage(msg, targetOrigin);
    }
  }

  downloadCsv() {
    const csvSchemas: CsvSchema[] = [
      { name: 'spinNumber', title: 'GAMEHISTORY.GAME.spin' },
      { name: 'type', title: 'GAMEHISTORY.GAME.type' },
      { name: 'bet', title: 'GAMEHISTORY.GAME.bet' },
      { name: 'win', title: 'GAMEHISTORY.GAME.win' },
      { name: 'ts', title: 'GAMEHISTORY.GAME.date' },
      { name: 'gameNameLabel', title: 'GAMEHISTORY.GRID.gameNameLabel', defaultValue: this.roundInfo.gameNameLabel },
      { name: 'gameCode', title: 'GAMEHISTORY.GRID.gameCode', defaultValue: this.roundInfo.gameCode },
      { name: 'roundId', title: 'GAMEHISTORY.GRID.roundId', defaultValue: String(this.roundInfo.roundId) },
      { name: 'playerCode', title: 'GAMEHISTORY.GRID.playerCode', defaultValue: this.roundInfo.playerCode },
    ];
    const filter = { values: { path: this.path }, pages: { limit: 10000 } };
    const filename = `${(this.roundInfo.gameNameLabel)} spins list of round ${(this.roundInfo.roundId)}`;
    this.spinService.getListPure(this.roundInfo.roundId, filter).subscribe(data => {
      const spins = JSON.parse(JSON.stringify(data));

      const rows = spins.map(spin => {
        transformSpin(spin, spin.currency);
        const { bet, spinNumber, ts, type, win } = spin;
        return {
          bet, spinNumber, ts, type, win
        };
      });
      this.csvService.toCsv(csvSchemas, rows, filename, rows.length === 10000);
    });
  }

  getSpinListWithDetails() {
    this.roundService.getSpinListWithDetails(this.path, this.roundInfo.roundId.toString())
      .pipe(
        take(1),
      )
      .subscribe(
        data => {
          this.spinListWithDetail = data;

          let appUrl = Array.isArray(data) && data.length && data[0]?.details?.historyInfo?.url;

          this.initIFrame(appUrl);
        },
        ( error: HttpErrorResponse ) => {
          this.notifications.error(error?.error?.message);
        });
  }

  onCopyRoundClick() {
    try {
      this.clipboard.copy(JSON.stringify(this.spinListWithDetail, null, 4));
      this.notifications.success(this.translateService.instant('GAMEHISTORY.GAME.notificationRoundCopy'), '');
    } catch (exception) {
      this.notifications.error(this.translateService.instant('GAMEHISTORY.GAME.notificationCopyFailed'), '');
    }
  }

  onCopySpinClick() {
    try {
      this.spinService.getItem({ path: this.path, roundId: this.roundInfo.roundId, spin: {}, spinNumber: this.selectedSpinId })
        .pipe(
          take(1),
          tap(() => {
            this.spinLoading = true;
          }))
        .subscribe(
          data => {
            this.clipboard.copy(JSON.stringify(data, null, 4));
            this.notifications.success(this.translateService.instant('GAMEHISTORY.GAME.notificationCopy'), '');
          },
          ( error: HttpErrorResponse ) => {
            this.notifications.error(error?.error?.message);
          },
          () => {
            this.spinLoading = false;
          });
    } catch (exception) {
    }
  }

  downloadSmResult() {
    this.roundService.getGameHistorySmResult(this.path, this.roundInfo.roundId.toString())
      .pipe(
        take(1)
      ).subscribe(( data: string ) => {
      const { name } = this.roundInfo._meta;
      const filename = `RoundId [${this.roundInfo.roundId}] entity name [${name}]`;
      simulateBrowserDownload(data, filename, FileType.Txt);
    });
  }
}
