import { Component, Inject, OnDestroy } from '@angular/core';
import { SwHubAuthService } from '@skywind-group/lib-swui';
import { Observable, Subscription } from 'rxjs';
import { map, take } from 'rxjs/operators';
import { FileType, simulateBrowserDownload } from '../../../../../common/lib/files';
import { JurisdictionService } from '../../../../../common/services/jurisdiction.service';
import { GameHistoryService } from '../../../../../common/services/reports/gamehistory.service';
import { GameHistorySpinService } from '../../../../../common/services/reports/gamehistory.spin.service';

import { GameHistory, GameHistorySpin } from '../../../../../common/typings';
import { GameHistoryModalInfo } from './iframe-view-modal/iframe-view-modal.component';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';

// tslint:disable-next-line
const noImage = 'data:image/png;base64,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';

@Component({
  selector: 'round-info-view-modal',
  templateUrl: './round-info-view-modal.component.html',
  styleUrls: ['./round-info-view-modal.component.scss']
})
export class RoundInfoViewModalComponent implements OnDestroy {
  readonly isSuperAdmin: boolean;
  readonly roundInfo: GameHistory;
  readonly path: string;
  readonly smResultAvailable$: Observable<boolean>;

  brief: any;
  spinItemsResponse;
  spinItem;
  showSmResult: boolean = true;

  noImage = noImage;

  private readonly subscriptions: Subscription[] = [];

  constructor(
    private readonly spinService: GameHistorySpinService<GameHistorySpin>,
    @Inject(MAT_DIALOG_DATA) { roundInfo }: GameHistoryModalInfo,
    private readonly roundService: GameHistoryService<GameHistory>,
    authService: SwHubAuthService,
    jurisdictionService: JurisdictionService
  ) {
    this.isSuperAdmin = authService.isSuperAdmin;
    this.roundInfo = roundInfo;
    this.path = this.roundInfo._meta.fullPath;

    this.smResultAvailable$ = jurisdictionService.entityJurisdiction.pipe(map(codes => codes.includes('PT')));

    this.subscriptions.push(
      spinService._items.subscribe(( data ) => {
        this.spinItemsResponse = data;
      }),
      spinService._item.subscribe(( data ) => {
        this.spinItem = data;
      })
    );
  }

  ngOnDestroy() {
    this.subscriptions.forEach(subscription => subscription.unsubscribe());
  }

  public footerBack() {
    this.spinService._item.next();
  }

  public footerPrev() {
    this.spinService.getPrev();
  }

  public footerNext() {
    this.spinService.getNext();
  }

  public sendEmail() {
    const mailto = 'mailto:<EMAIL>';
    const subject = encodeURIComponent('Game Details Request');
    const body = encodeURIComponent(`Operator ${this.brief.name}
    ${this.path ? 'Path ' + this.path : ''}
    Round ID ${this.roundInfo.roundId}
    Player ID ${this.roundInfo.playerCode}`);
    window.location.href = `${mailto}?subject=${subject}&body=${body}`;
  }

  public isSpinTypeAllow( type: string ): boolean {
    return [
      'force-finish', 'revert-game', 'finalize', 'noMoreBets', 'roundEnded', 'roundCanceled', 'rushBet'
    ].indexOf(type) === -1;
  }

  showSmResultEmitter( event ) {
    this.showSmResult = event;
  }

  downloadSmResult() {
    this.roundService.getGameHistorySmResult(this.path, this.roundInfo.roundId.toString())
      .pipe(
        take(1)
      ).subscribe(( data: string ) => {
      const { name } = this.roundInfo._meta;
      const filename = `RoundId [${this.roundInfo.roundId}] entity name [${name}]`;
      simulateBrowserDownload(data, filename, FileType.Txt);
    });
  }
}
