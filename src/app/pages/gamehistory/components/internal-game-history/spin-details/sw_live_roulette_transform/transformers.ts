import { IOriginalBet, ITransformedBet } from './interfaces';

const transformCommon = ( bets: IOriginalBet[], positionId: string ): ITransformedBet[] => {
  return bets.map(( { bet, win } ) => (
    {
      bet,
      win,
      positionId
    }
  ));
};

export const transformStraight = ( bets: IOriginalBet[] ): ITransformedBet[] => {
  return bets.map(( { bet, win, selector }: IOriginalBet ) => (
    {
      bet,
      win,
      positionId: selector[0]
    }
  ));
};

export const transformSplit = ( bets: IOriginalBet[] ): ITransformedBet[] => {
  return bets.map(( { bet, win, selector }: IOriginalBet ) => {
    let positionId = '0';

    switch (JSON.stringify(selector)) {
      case '["1","2"]':
        positionId = '37';
        break;
      case '["1","4"]':
        positionId = '38';
        break;
      case '["1","0"]':
        positionId = '39';
        break;
      case '["2","0"]':
        positionId = '40';
        break;
      case '["2","3"]':
        positionId = '41';
        break;
      case '["2","5"]':
        positionId = '42';
        break;
      case '["3","0"]':
        positionId = '43';
        break;
      case '["3","6"]':
        positionId = '44';
        break;
      case '["4","5"]':
        positionId = '45';
        break;
      case '["4","7"]':
        positionId = '46';
        break;
      case '["5","6"]':
        positionId = '47';
        break;
      case '["5","8"]':
        positionId = '48';
        break;
      case '["6","9"]':
        positionId = '49';
        break;
      case '["10","7"]':
        positionId = '50';
        break;
      case '["7","8"]':
        positionId = '51';
        break;
      case '["11","8"]':
        positionId = '52';
        break;
      case '["8","9"]':
        positionId = '53';
        break;
      case '["12","9"]':
        positionId = '54';
        break;
      case '["10","13"]':
        positionId = '55';
        break;
      case '["10","11"]':
        positionId = '56';
        break;
      case '["11","14"]':
        positionId = '57';
        break;
      case '["11","12"]':
        positionId = '58';
        break;
      case '["12","15"]':
        positionId = '59';
        break;
      case '["13","16"]':
        positionId = '60';
        break;
      case '["13","14"]':
        positionId = '61';
        break;
      case '["14","17"]':
        positionId = '62';
        break;
      case '["14","15"]':
        positionId = '63';
        break;
      case '["15","18"]':
        positionId = '64';
        break;
      case '["16","17"]':
        positionId = '65';
        break;
      case '["16","19"]':
        positionId = '66';
        break;
      case '["17","20"]':
        positionId = '67';
        break;
      case '["17","18"]':
        positionId = '68';
        break;
      case '["18","21"]':
        positionId = '69';
        break;
      case '["19","22"]':
        positionId = '70';
        break;
      case '["19","20"]':
        positionId = '71';
        break;
      case '["20","23"]':
        positionId = '72';
        break;
      case '["20","21"]':
        positionId = '73';
        break;
      case '["21","24"]':
        positionId = '74';
        break;
      case '["22","25"]':
        positionId = '75';
        break;
      case '["22","23"]':
        positionId = '76';
        break;
      case '["23","26"]':
        positionId = '77';
        break;
      case '["23","24"]':
        positionId = '78';
        break;
      case '["24","27"]':
        positionId = '79';
        break;
      case '["25","28"]':
        positionId = '80';
        break;
      case '["25","26"]':
        positionId = '81';
        break;
      case '["26","29"]':
        positionId = '82';
        break;
      case '["26","27"]':
        positionId = '83';
        break;
      case '["27","30"]':
        positionId = '84';
        break;
      case '["28","31"]':
        positionId = '85';
        break;
      case '["28","29"]':
        positionId = '86';
        break;
      case '["29","32"]':
        positionId = '87';
        break;
      case '["29","30"]':
        positionId = '88';
        break;
      case '["30","33"]':
        positionId = '89';
        break;
      case '["31","34"]':
        positionId = '90';
        break;
      case '["31","32"]':
        positionId = '91';
        break;
      case '["32","35"]':
        positionId = '92';
        break;
      case '["32","33"]':
        positionId = '93';
        break;
      case '["33","36"]':
        positionId = '94';
        break;
      case '["34","35"]':
        positionId = '95';
        break;
      case '["35","36"]':
        positionId = '96';
        break;
      default:
        break;
    }

    return {
      bet,
      win,
      positionId
    };
  });
};

export const transformStreet = ( bets: IOriginalBet[] ): ITransformedBet[] => {
  return bets.map(( { bet, win, selector }: IOriginalBet ) => {
    let positionId = '0';

    switch (JSON.stringify(selector)) {
      case '["0","1","2"]':
        positionId = '97';
        break;
      case '["0","2","3"]':
        positionId = '98';
        break;
      case '["1","2","3"]':
        positionId = '99';
        break;
      case '["4","5","6"]':
        positionId = '100';
        break;
      case '["7","8","9"]':
        positionId = '101';
        break;
      case '["10","11","12"]':
        positionId = '102';
        break;
      case '["13","14","15"]':
        positionId = '103';
        break;
      case '["16","17","18"]':
        positionId = '104';
        break;
      case '["19","20","21"]':
        positionId = '105';
        break;
      case '["22","23","24"]':
        positionId = '106';
        break;
      case '["25","26","27"]':
        positionId = '107';
        break;
      case '["28","29","30"]':
        positionId = '108';
        break;
      case '["31","32","33"]':
        positionId = '109';
        break;
      case '["34","35","36"]':
        positionId = '110';
        break;
      default:
        break;
    }

    return {
      bet,
      win,
      positionId
    };
  });
};

export const transformCorner = ( bets: IOriginalBet[] ): ITransformedBet[] => {
  return bets.map(( { bet, win, selector }: IOriginalBet ) => {
    let positionId = '0';

    switch (JSON.stringify(selector)) {
      case '["0","1","2","3"]':
        positionId = '111';
        break;
      case '["1","2","4","5"]':
        positionId = '112';
        break;
      case '["2","3","5","6"]':
        positionId = '113';
        break;
      case '["4","5","7","8"]':
        positionId = '114';
        break;
      case '["5","6","8","9"]':
        positionId = '115';
        break;
      case '["10","11","7","8"]':
        positionId = '116';
        break;
      case '["11","12","8","9"]':
        positionId = '117';
        break;
      case '["10","11","13","14"]':
        positionId = '118';
        break;
      case '["11","12","14","15"]':
        positionId = '119';
        break;
      case '["13","14","16","17"]':
        positionId = '120';
        break;
      case '["14","15","17","18"]':
        positionId = '121';
        break;
      case '["16","17","19","20"]':
        positionId = '122';
        break;
      case '["17","18","20","21"]':
        positionId = '123';
        break;
      case '["19","20","22","23"]':
        positionId = '124';
        break;
      case '["20","21","23","24"]':
        positionId = '125';
        break;
      case '["22","23","25","26"]':
        positionId = '126';
        break;
      case '["23","24","26","27"]':
        positionId = '127';
        break;
      case '["25","26","28","29"]':
        positionId = '128';
        break;
      case '["26","27","29","30"]':
        positionId = '129';
        break;
      case '["28","29","31","32"]':
        positionId = '130';
        break;
      case '["29","30","32","33"]':
        positionId = '131';
        break;
      case '["32","32","34","36"]':
        positionId = '132';
        break;
      case '["32","33","35","36"]':
        positionId = '133';
        break;
      default:
        break;
    }

    return {
      bet,
      win,
      positionId
    };
  });
};

export const transformLine = ( bets: IOriginalBet[] ): ITransformedBet[] => {
  return bets.map(( { bet, win, selector }: IOriginalBet ) => {
    let positionId = '0';

    switch (JSON.stringify(selector)) {
      case '["1","2","3","4","5","6"]':
        positionId = '134';
        break;
      case '["4","5","6","7","8","9"]':
        positionId = '135';
        break;
      case '["10","11","12","7","8","9"]':
        positionId = '136';
        break;
      case '["10","11","12","13","14","15"]':
        positionId = '137';
        break;
      case '["13","14","15","16","17","18"]':
        positionId = '138';
        break;
      case '["16","17","18","19","20","21"]':
        positionId = '139';
        break;
      case '["19","20","21","22","23","24"]':
        positionId = '140';
        break;
      case '["22","23","24","25","26","27"]':
        positionId = '141';
        break;
      case '["25","26","27","28","29","30"]':
        positionId = '142';
        break;
      case '["28","29","30","31","32","33"]':
        positionId = '143';
        break;
      case '["31","32","33","34","35","36"]':
        positionId = '144';
        break;
      default:
        break;
    }

    return {
      bet,
      win,
      positionId
    };
  });
};

export const transformColumn = ( bets: IOriginalBet[] ): ITransformedBet[] => {
  return bets.map(( { bet, win, selector }: IOriginalBet ) => {
    let positionId = '145';

    if (selector.includes('2')) {
      positionId = '146';
    } else if (selector.includes('3')) {
      positionId = '147';
    }

    return {
      bet,
      win,
      positionId
    };
  });
};

export const transformDozen = ( bets: IOriginalBet[] ): ITransformedBet[] => {
  return bets.map(( { bet, win, selector }: IOriginalBet ) => {
    let positionId = '148';

    if (selector.includes('13')) {
      positionId = '149';
    } else if (selector.includes('25')) {
      positionId = '150';
    }

    return {
      bet,
      win,
      positionId
    };
  });
};

export const transformRed = ( bets: IOriginalBet[] ): ITransformedBet[] => {
  return transformCommon(bets, '151');
};

export const transformBlack = ( bets: IOriginalBet[] ): ITransformedBet[] => {
  return transformCommon(bets, '152');
};

export const transformLow = ( bets: IOriginalBet[] ): ITransformedBet[] => {
  return transformCommon(bets, '153');
};

export const transformHigh = ( bets: IOriginalBet[] ): ITransformedBet[] => {
  return transformCommon(bets, '154');
};

export const transformOdd = ( bets: IOriginalBet[] ): ITransformedBet[] => {
  return transformCommon(bets, '155');
};

export const transformEven = ( bets: IOriginalBet[] ): ITransformedBet[] => {
  return transformCommon(bets, '156');
};
