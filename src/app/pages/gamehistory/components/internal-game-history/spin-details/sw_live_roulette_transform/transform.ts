import { IOriginalBets, IOriginalDetails, ITransformedBet, ITransformedDetails } from './interfaces';
import {
  transformBlack, transformColumn, transformCorner, transformDozen, transformEven, transformHigh, transformLine, transformLow, transformOdd,
  transformRed, transformSplit, transformStraight, transformStreet,
} from './transformers';

const mappings = {
  straight: transformStraight,
  split: transformSplit,
  street: transformStreet,
  corner: transformCorner,
  line: transformLine,
  column: transformColumn,
  dozen: transformDozen,
  red: transformRed,
  black: transformBlack,
  low: transformLow,
  high: transformHigh,
  odd: transformOdd,
  even: transformEven
};

const transformBets = ( originalBets: IOriginalBets ): ITransformedBet[] => {
  let transformedBets: ITransformedBet[] = [];

  Object.keys(originalBets).forEach(( betType ) => {
    transformedBets = [...transformedBets, ...(mappings[betType] ? mappings[betType](originalBets[betType]) : [])];
  });

  return transformedBets;
};

export const transformHistoryResponse = ( originalDetails: IOriginalDetails ): ITransformedDetails => {

  return {
    betStates: transformBets(originalDetails.payload.table.bets),
    request: 'play',
    roundEnded: originalDetails.roundEnded,
    stopPosition: originalDetails.payload.ballPosition,
    totalBet: 0,
    totalWin: originalDetails.totalWin
  };
};

export default transformHistoryResponse;
