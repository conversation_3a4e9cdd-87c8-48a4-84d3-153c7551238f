export interface IOriginalBet {
  bet: number;
  isWin: boolean;
  ratio: number;
  selector?: string[];
  win: number;
}

export interface IOriginalBets {
  [key: string]: IOriginalBet[];
}

export interface IOriginalDetails {
  payload: {
    ballPosition: string;
    bets: any;
    roundId: string;
    settings: any;
    table: {
      bets: IOriginalBets;
    }
    totalWin: number;
  };
  roundEnded: boolean;
  timestamp: number;
  totalWin: number;
}

export interface ITransformedBet {
  bet: number;
  win: number;
  positionId: string;
}

export interface ITransformedDetails {
  betStates: ITransformedBet[];
  request: string;
  roundEnded: boolean;
  stopPosition: string;
  totalBet: number;
  totalWin: number;
}
