.mat-table {
  width: 100%;

  .mat-row {
    height: 36px;

    &:nth-child(even) {
      background-color: #f9f9fa;
    }

    &:nth-child(odd) {
      background-color: #eef1f5;
    }
  }

  .mat-cell {
    font-size: 14px;
    white-space: nowrap;

    &:first-child {
      font-weight: 500;
    }

    .alert-message + .alert-message {
      margin-left: 5px;
    }
  }
}

.alert-message {
  padding: 8px;
  border-radius: 4px;
  margin-bottom: 10px;

  &--danger {
    color: #f44336;
    background-color: rgb(253, 236, 234);
  }

  &--warning {
    color: rgb(102, 60, 0);
    background-color: rgb(255, 244, 229);
  }
}

#gameContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px;
  background: #000;
  width: 100%;
}

.spinner-wrapper {
  position: absolute;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.2);
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.l7-jackpot {
  display: flex;
  justify-content: center;
  background: #000;
}

.instant-jackpot {
  display: flex;
  justify-content: center;
  background: #000;

  img {
    max-width: 100%;
  }

  &.large {
    padding: 100px;
  }
}
