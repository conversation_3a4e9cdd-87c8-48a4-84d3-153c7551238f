.tableSrt,
.tableRedeem,
.tablePhTournament,
.tablePrizeDrop,
.tableMustWinJackpot,
.tableSharedJackpotPrize {
  background-color: #131314;
  color: white;
  padding-bottom: 45px;

  .table {
    border-collapse: collapse;
  }

  td {
    position: relative;
    height: 40px;
    vertical-align: middle;
    background-color: #18181a;
    white-space: nowrap;
    padding: 0 20px;
    color: white;
    text-align: right;
  }

  th {
    height: 40px;
    padding: 0 20px;
    font-family: font-verdana;
    color: rgba(160, 160, 160, 0.5);
    text-transform: uppercase;
    text-align: left;
    vertical-align: middle;
  }

  tr {
    &:nth-child(even) {
      td, th {
        border: 0;
        background-color: #18181a
      }
    }

    &:nth-child(odd) {
      td, th {
        border: 0;
        background-color: #1b1b1c;
      }
    }
  }

  thead {
    th {
      background-color: transparent !important;
      color: #fff;
    }
  }
}
