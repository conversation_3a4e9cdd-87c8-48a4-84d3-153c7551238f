import { ChangeDetectorRef, Component, Injector, Input, ViewChild, ViewContainerRef } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { SwHubAuthService, SwuiNotificationsService } from '@skywind-group/lib-swui';
import { LOCALE_LIST } from '../../../../../app.constants';
import { keysToRemoveForPureJSON } from '../../../../../common/typings';

import { checkIfBalanceHidden } from '../base-history/base-history.component';
import { SWGamehistory } from './sw-history.plugin';
import { transformGhSpinDetails, transformSpin, transformSpinDetails } from '../../../../../common/core/currecy-transform';

export const L7JP_POOLS = {
  'A': 'img/poolA.png',
  'B': 'img/poolB.png',
  'C': 'img/poolC.png',
  'D': 'img/poolD.png'
};

const INSTANT_JACKPOT: string = 'img/instantJackpot.png';

export enum GAME_RENDERER {
  plain,
  canvas,
  labelless,
}

@Component({
  selector: 'spin-details',
  templateUrl: './spin-details.component.html',
  styleUrls: ['./spin-details.component.scss'],
})
export class SpinDetailsComponent {

  @Input() public roundInfo: any = {};
  @Input() public activeSpin: any = {};

  @ViewChild('dynamic', { read: ViewContainerRef }) dynamicComponentContainer: ViewContainerRef;
  @ViewChild('bonus', { read: ViewContainerRef }) dynamicBonusComponentContainer: ViewContainerRef;

  public errorMessage: string;
  public loading: boolean = true;

  public subscriptions: any[] = [];
  public isLoaded;
  public instantJackpotImg = INSTANT_JACKPOT;

  public ghComponent;
  public ghIframeSelector = 'gameContainer';

  public spin: any;
  public pureSpin: any;
  public parsedSpin: any;

  public canvas: boolean;

  public balanceBeforeVisible = true;
  public balanceAfterVisible = true;
  public isCoinVisible = false;

  constructor( private readonly notifications: SwuiNotificationsService,
               private readonly injector: Injector,
               private readonly changeDetectorRef: ChangeDetectorRef,
               private readonly authService: SwHubAuthService,
               private readonly translate: TranslateService,
  ) {
  }

  ngOnChanges() {
    const hideBalanceBeforeAndAfter = this.roundInfo?._meta?.hideBalanceBeforeAndAfter;

    this.balanceBeforeVisible = !checkIfBalanceHidden(this.authService) && !hideBalanceBeforeAndAfter;
    this.balanceAfterVisible = !checkIfBalanceHidden(this.authService) && !hideBalanceBeforeAndAfter;

    this.pureSpin = this.getSpin();
    this.spin = JSON.parse(JSON.stringify(this.getSpin()));
    transformSpin(this.spin, this.spin.currency);
    transformSpinDetails(this.spin, this.spin.currency);
    this.parsedSpin = this.spin;
    this.canvas = this.spin.historyInfo && this.spin.historyInfo.historyRenderType !== GAME_RENDERER.plain;
    this.isCoinVisible = this.coinVisible;

    this.setFreeSpin();
    this.setJackpot();
    this.setFreeBet();
    this.setRespin();
    this.setMaxWin();
    this.setBonus();
    this.setFreeSpinsCount();
  }

  ngAfterViewInit() {
    const translateService = this.injector.get(TranslateService);
    const language = LOCALE_LIST.find(locale => locale.id === translateService.currentLang).dialect;

    const appUrl: string = this.spin.historyInfo.url.replace('/index.html', '/history.html');
    this.ghComponent = SWGamehistory.getInstance(appUrl, { theme: 'white', language: language, currency: this.spin.currency });

    window.addEventListener('message', ( e: any ) => {
      if (e.data.type === 'parsedSpin') {
        const data = JSON.parse(e.data.spin);
        this.parsedSpin = Object.assign(this.spin, data);
      }

      this.changeDetectorRef.markForCheck();
    });

    this.setupPage();
  }

  ngOnDestroy() {
    this.changeDetectorRef.detach();
    this.subscriptions.forEach(subscription => subscription.unsubscribe());
    this.ghComponent.remove(this.ghIframeSelector);
  }

  isSuperAdmin(): boolean {
    return this.authService.isSuperAdmin;
  }

  public setupPage() {
    if ((!this.canvas && !this.isRedeemBns()) ||
      !(this.canvas &&
        (this.isSrt() ||
          this.isJackpotSrtChallenge() ||
          this.isJackpotSrtTournament() ||
          this.isRedeemBns() ||
          this.isPhTournament() ||
          this.isPrizeDrop() ||
          this.isMustWinJackpot() ||
          this.isSharedJackpotPrize() ||
          this.isL7Jackpot() ||
          this.isInstantJackpotPlay()))
    ) {
      const spin = JSON.parse(JSON.stringify(this.pureSpin));
      transformGhSpinDetails(spin, spin.currency);
      this.ghComponent.commitData(this.ghIframeSelector, spin);
      this.ghComponent.isLoaded.subscribe(( data ) => this.isLoaded = data);
    }
  }

  public getWin(): string {
    return this.parsedSpin.credit || this.parsedSpin.win ||
      (this.parsedSpin.details ? this.parsedSpin.details.totalWin : undefined);
  }

  public getBet(): string | number {
    if (this.parsedSpin.isFreeBet) {
      return 0;
    }

    if (this.parsedSpin.bet !== undefined) {
      return this.parsedSpin.bet;
    } else {
      if (this.parsedSpin.details !== undefined) {
        return this.parsedSpin.details.totalBet;
      } else {
        return undefined;
      }
    }
  }

  public getBalanceAfter(): string {
    if (('balanceAfter' in this.parsedSpin) && (this.parsedSpin.balanceAfter !== null)) {
      return this.parsedSpin.balanceAfter;
    } else {
      return '-';
    }
  }

  public getBalanceBefore(): string {
    if (('balanceBefore' in this.parsedSpin) && (this.parsedSpin.balanceBefore !== null)) {
      return this.parsedSpin.balanceBefore;
    } else {
      return '-';
    }
  }

  public isLiveCasino(): boolean {
    const liveCasinoTypes = ['noMoreBets', 'roundEnded', 'roundCanceled'];

    return liveCasinoTypes.indexOf(this.spin.type) !== -1;
  }

  public isJackpotSrtTournament() {
    return this.spin && this.spin.details && Array.isArray(this.spin.details.result) &&
      this.spin.details.result[0].event === 'win' &&
      (this.spin.details.result[0].pool === 'pool0' ||
        this.spin.details.result[0].pool === 'pool1' ||
        this.spin.details.result[0].pool === 'pool2' ||
        this.spin.details.result[0].pool === 'pool3'
      );
  }

  public isJackpotSrtChallenge() {
    return this.spin && this.spin.details && Array.isArray(this.spin.details.result) &&
      this.spin.details.result[0].event === 'win' &&
      this.spin.details.result[0].pool === 'prize_pool';
  }

  public isSrt() {
    return this.activeSpin.type === 'srt-tournament-end' ||
      this.activeSpin.type === 'srt-challenge-bonus' ||
      this.activeSpin.type === 'srt-vip-redeem';
  }

  public isPiggyRewards() {
    return this.isSrtType('srt-vip-redeem');
  }

  public isSrtTournamentEnd() {
    return this.isSrtType('srt-tournament-end');
  }

  public isSrtChallengeBonus() {
    return this.isSrtType('srt-challenge-bonus');
  }

  public isRedeemBns(): boolean {
    return this.spin.type === 'redeem-bns';
  }

  public isPhTournament(): boolean {
    return this.spin && ['ph-tournament-win', 'tournament'].indexOf(this.spin.type) !== -1;
  }

  public isPrizeDrop(): boolean {
    return this.spin && ['prizeDrop', 'prize_win'].indexOf(this.spin.type) !== -1;
  }

  public isStand(): boolean {
    return ['stand'].indexOf(this.spin.details.request) !== -1;
  }

  public isMustWinJackpot() {
    return this.spin && this.spin.details && this.spin.details.result && Array.isArray(this.spin.details.result) &&
      this.spin.details.result.some(item => item.jackpotType && item.jackpotType.indexOf('ph-must-win') !== -1);
  }

  public isSharedJackpotPrize() {
    return this.spin && this.spin.details && this.spin.details.paymentType &&
      this.spin.details.paymentType === 'shared_jp_prize';
  }

  public isL7Jackpot(): boolean {
    return this.spin && this.spin.details && Array.isArray(this.spin.details.result) &&
      this.spin.details.result[0].event === 'win' && this.spin.details.result[0].jackpotType === 'sw-instant-jp' &&
      !!L7JP_POOLS[this.spin.details.result[0].pool] && this.spin.historyInfo.historyRenderType !== 0;
  }

  public isInstantJackpotPlay(): boolean {
    return this.spin && this.spin.details && this.spin.details.request && (typeof this.spin.details.request === 'string')
      && this.spin.details.request.includes('-instant-jp-mini-game') && (['bonusgame', 'jackpot-mini-game'].includes(this.spin.type));
  }

  public getL7JackpotImage(): string {
    return this.isL7Jackpot() && L7JP_POOLS[this.spin.details.result[0].pool] || '';
  }

  public getInstantJackpotPlay(): string {
    if (!this.isInstantJackpotPlay()) {
      return '';
    }

    if (this.spin && this.spin.details && this.spin.details.request === 'start-instant-jp-mini-game') {
      return this.instantJackpotImg;
    }

    const eventNumber = [...this.spin.details.roundsInfo.rounds[0].selectedIds].pop();
    const type = this.spin.details.roundsInfo.rounds[0].items[eventNumber].visibleValue;

    return L7JP_POOLS[type];
  }

  public isDisconnect() {
    return this.spin.type === 'disconnect';
  }

  public onCopyJsonSuccess() {
    const message = this.translate.instant('GAMEHISTORY.GAME.notificationCopy');
    this.notifications.success(message, '');
  }

  public getCopyJsonFn() {
    let spin = JSON.parse(JSON.stringify(this.activeSpin));

    keysToRemoveForPureJSON.forEach(key => {
      delete spin[key];
    });

    if (spin.details && spin.details.state) {
      delete spin.details.state.freeSpinsCount;
    }

    return () => JSON.stringify(spin, null, 4);
  }

  private get coinVisible(): boolean {
    if (!this.spin || !this.spin.jrsdSettings) {
      return true;
    }

    return !(this.spin.jrsdSettings.modeCoinAllowed === false);
  }

  private isSrtType( type: string ) {
    return this.spin && this.spin.type === type;
  }

  private setFreeBet(): void {
    if (this.spin.details) {
      this.spin.isFreeBet = !!this.spin.details.isFreeBet;
    }
  }

  private setFreeSpin(): void {
    if (this.isNewGame()) {
      if (this.spin.type && this.spin.type === 'freegame') {
        this.spin.isFreeSpin = true;
      }
    } else {
      if (this.spin.details) {
        const reelsSet = ((this.spin.details.reels && this.spin.details.reels.set) || '').toLowerCase();
        const detailsState = this.spin.details.state || {};
        const currentScene = (detailsState.currentScene || '').toLowerCase();
        const freeSpinsWin = detailsState.freeSpinsWin || 0;

        this.spin.isFreeSpin = (
          (reelsSet
            ? reelsSet.indexOf('free') > -1
            : (currentScene.indexOf('free') > -1 && this.spin.details.request !== 'bonusSelection'))
          || (currentScene.indexOf('main') > -1 && freeSpinsWin > 0)
          || (this.spin.details.scene && this.spin.details.scene.indexOf('free') > -1)
          || (this.spin.type && this.spin.type === 'freegame')
        );
      }
    }
  }

  private setJackpot(): void {
    if (this.spin.type === 'jackpot-mini-game' || this.spin.type === 'jackpot-win') {
      this.spin.isJackpot = true;
    }
  }

  private setRespin(): void {
    if (this.isNewGame()) {
      if (this.spin.type && this.spin.type === 'respin') {
        this.spin.isRespin = true;
      }
    } else {
      if (this.spin.gameCode === 'sw_sod' || this.spin.gameId === 'sw_sod') {
        if (this.spin.details.spinType === 'reSpin') {
          this.spin.isRespin = true;
        }
        return;
      }
      if (this.spin.details && this.spin.details.reels &&
        this.spin.details.reels.set && this.spin.details.reels.set.indexOf('respin') !== -1) {
        this.spin.isRespin = true;

        return;
      }
      this.spin.isRespin = this.spin.details && this.spin.details.state &&
        (this.spin.details.state.isRespin === true
          || this.spin.details.scene === 'reSpinScene'
        ) || (
          this.spin.type && this.spin.type === 'respin'
        );
      if (this.spin.isRespin === undefined) {
        const state = (this.spin.details && this.spin.details.state) || {};
        const spinNumber = this.spin.spinNumber && parseInt(this.spin.spinNumber, 10);
        this.spin.isRespin = Object.keys(state)
          .map(k => k.toLowerCase())
          .indexOf('respinwin') > -1 && spinNumber > 0;
      }
    }
  }

  private setMaxWin() {
    if (this.spin.details.events) {
      const data = this.spin.details.events.find(el => el.id === 'maxWinReached');
      if (data) {
        this.spin.maxWinReached = data;
      }
    }
  }

  private setBonus() {
    let jackpotEndEvent;
    if (this.spin.details.events) {
      jackpotEndEvent = this.spin.details.events.find(el => el.id === 'jackpotEnd');
    }
    if ((this.spin.type === 'bonusgame')
      || (this.spin.type === 'jackpot-mini-game' && jackpotEndEvent && !jackpotEndEvent.data)) {
      this.spin.isBonus = true;
    }
  }

  private getSpin() {
    if (!this.activeSpin.balanceBefore && this.activeSpin.balanceBefore !== 0) {
      this.activeSpin.balanceBefore = null;
    }
    if (!this.activeSpin.balanceAfter && this.activeSpin.balanceAfter !== 0) {
      this.activeSpin.balanceAfter = null;
    }

    return Object.assign({}, this.roundInfo, this.activeSpin);
  }

  private isNewGame() {
    const renderType = this.spin.historyInfo.historyRenderType;
    return renderType === GAME_RENDERER.canvas || renderType === GAME_RENDERER.labelless;
  }

  private setFreeSpinsCount() {
    const state = this.spin.details.state;

    if (state) {
      if (state.isReSpin === true) {
        state.freeSpinsNumber = (state.totalFreeSpinsCount -
          (state.freeSpinsCount - 1));
        if (state.freeSpinsNumber > state.totalFreeSpinsCount) {
          state.freeSpinsNumber = state.totalFreeSpinsCount;
        }
      } else {
        if (this.spin.details.request !== 'bonusSelection') {
          state.freeSpinsNumber = (this.spin.details.state.totalFreeSpinsCount - state.freeSpinsCount);
        }
      }
    }
  }
}
