<div *ngIf="errorMessage" class="alert-message alert-message--danger">
  {{errorMessage}}
</div>
<div fxLayout="row" *ngIf="parsedSpin" class="margin-bottom24">
  <div fxFlex="50">
    <table class="mat-table">
      <tbody>
      <tr class="mat-row">
        <td class="mat-cell" width="145">
              <span *ngIf="isLiveCasino(); else notLiveCasino;">
                {{'GAMEHISTORY.GAME.operationId'| translate}}:
              </span>
          <ng-template #notLiveCasino>
            <span> {{'GAMEHISTORY.GAME.spinId'| translate}}:</span>
          </ng-template>
        </td>
        <td class="mat-cell">
          <div fxLayout="row">
            <span>{{ roundInfo.roundId }}#{{ parsedSpin.spinNumber }}</span>
            <span>{{ parsedSpin.state?.currentScene }}</span>
            <div fxLayoutAlign="end center">
                <span *ngIf="isSuperAdmin()" class="margin-left24">
                <a clipboard
                   href="#"
                   (click)="$event.preventDefault()"
                   (success)="onCopyJsonSuccess()"
                   [textFn]="getCopyJsonFn()">
                  {{ 'GAMEHISTORY.GAME.copyJsonToClipboard' | translate }}
                </a>
              </span>
            </div>
          </div>
        </td>
      </tr>
      <tr class="mat-row">
        <td class="mat-cell" width="145">
          <span>{{ 'GAMEHISTORY.GAME.operationType' | translate }}:</span>
        <td class="mat-cell">
          <span class="alert-message alert-message--warning"
                *ngIf="parsedSpin?.isFreeSpin && !parsedSpin?.isRespin">
            {{ 'GAMEHISTORY.GAME.freespin'| translate }}
          </span>
          <span class="alert-message alert-message--warning"
                *ngIf="parsedSpin.isRespin">
            {{ 'GAMEHISTORY.GAME.respin' | translate }}
          </span>
          <span class="alert-message alert-message--warning"
                *ngIf="parsedSpin.isBonus">
            {{ 'GAMEHISTORY.GAME.bonus' | translate }}
          </span>
          <span class="alert-message alert-message--warning"
                *ngIf="(parsedSpin.isJackpot && !isJackpotSrtChallenge() && !parsedSpin.isBonus) || isSharedJackpotPrize()">
            {{ 'GAMEHISTORY.GAME.jackpot' | translate }}
          </span>
          <span class="alert-message alert-message--warning"
                *ngIf="parsedSpin.maxWinReached">
            {{ 'GAMEHISTORY.GAME.maxWinReached' | translate }}
          </span>
          <span class="alert-message alert-message--warning"
                *ngIf="parsedSpin.isFreeBet">
            {{ 'GAMEHISTORY.GAME.freebet' | translate }}
          </span>
          <span class="alert-message alert-message--warning"
                *ngIf="parsedSpin.additionalLabel">
            {{ parsedSpin.additionalLabel }}
          </span>
          <span class="alert-message alert-message--warning"
                *ngIf="isStand()">
            {{ 'GAMEHISTORY.GAME.stand' | translate }}
          </span>
          <span class="alert-message alert-message--warning"
                *ngIf="isRedeemBns()">
            {{ 'GAMEHISTORY.GAME.coinRedeems' | translate }}
          </span>
          <span class="alert-message alert-message--warning"
                *ngIf="isPiggyRewards()">
            {{ 'GAMEHISTORY.GAME.coinRedeems' | translate }}
          </span>
          <span class="alert-message alert-message--warning"
                *ngIf="isSrtTournamentEnd()">
            {{ 'GAMEHISTORY.GAME.SRT.tournament' | translate }}
          </span>
          <span class="alert-message alert-message--warning"
                *ngIf="isSrtChallengeBonus()">
            {{ 'GAMEHISTORY.GAME.SRT.challenge' | translate }}
          </span>
          <span class="alert-message alert-message--warning"
                *ngIf="isJackpotSrtChallenge()">
            {{ 'GAMEHISTORY.GAME.SRT.challengeWinPool' | translate }}
          </span>
          <span class="alert-message alert-message--warning"
                *ngIf="isPhTournament()">
            {{ 'GAMEHISTORY.GAME.SRT.tournament' | translate }}
          </span>
          <span class="alert-message alert-message--warning"
                *ngIf="isPrizeDrop()">
            {{ 'GAMEHISTORY.GAME.prizeWin' | translate }}
          </span>
          <span class="alert-message alert-message--warning"
                *ngIf="parsedSpin?.details?.isFinalization">
            {{ 'GAMEHISTORY.GAME.finalization' | translate }}
          </span>
          <span class="alert-message alert-message--warning"
                *ngIf="isDisconnect()">
            {{ 'GAMEHISTORY.GAME.disconnect' | translate }}
          </span>
        </td>
      </tr>
      <tr *ngIf="parsedSpin.currency" class="mat-row">
        <td class="mat-cell" width="145">
          <span>{{ 'GAMEHISTORY.GAME.currency' | translate }}:</span>
        </td>
        <td class="mat-cell">
          {{ parsedSpin.currency }}
        </td>
      </tr>
      <tr *ngIf="parsedSpin.type" class="mat-row">
        <td class="mat-cell" width="145">
          <span>{{ 'GAMEHISTORY.GAME.type' | translate }}:</span>
        </td>
        <td class="mat-cell">{{parsedSpin.type}}</td>
      </tr>
      <tr *ngIf="parsedSpin.endOfRound !== undefined" class="mat-row">
        <td class="mat-cell" width="145">
          <span>{{ 'GAMEHISTORY.GAME.endOfRound' | translate }}:</span>
        </td>
        <td class="mat-cell">{{parsedSpin.endOfRound ? 'Yes' : 'No'}}</td>
      </tr>
      <tr *ngIf="parsedSpin.ts" class="mat-row">
        <td class="mat-cell" width="145">
          <span>{{ 'GAMEHISTORY.GAME.date' | translate }}:</span>
        </td>
        <td class="mat-cell">{{parsedSpin.ts}}</td>
      </tr>
      <tr *ngIf="parsedSpin.gameVersion" class="mat-row">
        <td class="mat-cell" width="145">
          <span translate>{{ 'GAMEHISTORY.GAME.gameVersion' | translate }}:</span>
        </td>
        <td class="mat-cell">{{parsedSpin.gameVersion}}</td>
      </tr>
      <tr *ngIf="parsedSpin.gameCode" class="mat-row">
        <td class="mat-cell" width="145">
          <span>{{ 'GAMEHISTORY.GRID.gameCode' | translate }}:</span>
        </td>
        <td class="mat-cell">{{parsedSpin.gameCode}}</td>
      </tr>
      <tr *ngIf="parsedSpin.gameId" class="mat-row">
        <td class="mat-cell" width="145">
          <span>{{ 'GAMEHISTORY.GAME.gameId' | translate }}:</span>
        </td>
        <td class="mat-cell">{{parsedSpin.gameId}}</td>
      </tr>
      <tr *ngIf="parsedSpin?.details?.payload?.tableName" class="mat-row">
        <td class="mat-cell" width="145">
          <span translate>{{ 'GAMEHISTORY.GAME.tableName' | translate }}:</span>
        </td>
        <td class="mat-cell">{{parsedSpin.details.payload.tableName}}</td>
      </tr>
      </tbody>
    </table>
  </div>

  <div fxFlex="50" class="margin-left24">
    <table class="mat-table">
      <tbody>
      <tr *ngIf="parsedSpin.details?.stake" class="mat-row">
        <td class="mat-cell" width="145">
            <span>
              {{ 'GAMEHISTORY.GAME.stake' | translate }}:
            </span>
          <span class="margin-left-5">
              {{ (isCoinVisible ? 'GAMEHISTORY.GAME.betCoinLines' : 'GAMEHISTORY.GAME.betLines') | translate }}
            </span>
        </td>
        <td class="mat-cell">
          <div fxLayoutAlign="end center">
            {{parsedSpin.details?.totalBet | formattedMoney: 2: ' ': parsedSpin.currency}} / <ng-container *ngIf="isCoinVisible">{{parsedSpin.details?.stake.bet | formattedMoney: 2: ' ': parsedSpin.currency}} /</ng-container> {{parsedSpin.details?.stake.lines}}
          </div>
        </td>
      </tr>
      <tr class="mat-row" *ngIf="parsedSpin.balanceBefore !== undefined && balanceBeforeVisible">
        <td class="mat-cell" width="145">
          <span>{{'GAMEHISTORY.GAME.balanceBefore' | translate }}:</span>
        </td>
        <td class="mat-cell">
          <div fxLayoutAlign="end center">
            {{ getBalanceBefore() | formattedMoney: 2: ' ': parsedSpin.currency }}
          </div>
        </td>
      </tr>
      <tr class="mat-row" *ngIf="getBet() !== undefined">
        <td class="mat-cell" width="145">
          <span>{{'GAMEHISTORY.GAME.bet' | translate}}:</span>
        </td>
        <td class="mat-cell">
          <div fxLayoutAlign="end center">
            {{getBet() | formattedMoney: 2: ' ': parsedSpin.currency}}
          </div>
        </td>
      </tr>
      <tr class="mat-row" *ngIf="getWin() !== undefined">
        <td class="mat-cell" width="145">
          <span>{{'GAMEHISTORY.GAME.win' | translate}}:</span>
        </td>
        <td class="mat-cell">
          <div fxLayoutAlign="end center">
            {{getWin() | formattedMoney: 2: ' ': parsedSpin.currency}}
          </div>
        </td>
      </tr>
      <tr class="mat-row" *ngIf="parsedSpin.isFreeBet">
        <td class="mat-cell" width="145">
          <span>{{'PROMO.EDIT.QUALIFYING_GAMES.freebetCoinValue' | translate}}:</span>
        </td>
        <td class="mat-cell">
          <div fxLayoutAlign="end center">
            {{parsedSpin.freeBetCoin || '-'}}
          </div>
        </td>
      </tr>
      <tr class="mat-row" *ngIf="parsedSpin.balanceAfter !== undefined && balanceAfterVisible">
        <td class="mat-cell" width="145">
          <span>{{'GAMEHISTORY.GAME.balanceAfter' | translate}}:</span>
        </td>
        <td class="mat-cell">
          <div fxLayoutAlign="end center">
            {{getBalanceAfter() | formattedMoney: 2: ' ': parsedSpin.currency}}
          </div>
        </td>
      </tr>
      <tr class="mat-row" *ngIf="parsedSpin.credit && parsedSpin.credit !== 0">
        <td class="mat-cell" width="145">
          <span>{{'GAMEHISTORY.GAME.credit' | translate}}:</span>
        </td>
        <td class="mat-cell">
          <div fxLayoutAlign="end center">
            {{parsedSpin.credit | formattedMoney: 2: ' ': parsedSpin.currency}}
          </div>
        </td>
      </tr>
      <tr class="mat-row" *ngIf="parsedSpin.debit && parsedSpin.debit !== 0">
        <td class="mat-cell" width="145">
          <span>{{'GAMEHISTORY.GAME.debit' | translate}}:</span>
        </td>
        <td class="mat-cell">
          <div fxLayoutAlign="end center">
            {{parsedSpin.debit | formattedMoney: 2: ' ': parsedSpin.currency}}
          </div>
        </td>
      </tr>
      <tr class="mat-row" *ngIf="parsedSpin.totalJpContribution && parsedSpin.totalJpContribution !== 0">
        <td class="mat-cell" width="145">
          <span>{{'GAMEHISTORY.GRID.totalJpContribution' | translate}}:</span>
        </td>
        <td class="mat-cell">
          <div fxLayoutAlign="end center">
            {{parsedSpin.totalJpContribution | formattedMoney: 2: ' ': parsedSpin.currency}}
          </div>
        </td>
      </tr>
      <tr class="mat-row" *ngIf="parsedSpin.totalJpWin && parsedSpin.totalJpWin !== 0">
        <td class="mat-cell" width="145">
          <span>{{'GAMEHISTORY.GRID.totalJpWin' | translate}}:</span>
        </td>
        <td class="mat-cell">
          <div fxLayoutAlign="end center">
            {{parsedSpin.totalJpWin | formattedMoney: 2: ' ': parsedSpin.currency}}
          </div>
        </td>
      </tr>
      <tr class="mat-row" *ngIf="parsedSpin.isPayment !== undefined">
        <td class="mat-cell" width="145">
          <span>{{'GAMEHISTORY.GAME.isPayment' | translate}}:</span>
        </td>
        <td class="mat-cell">
          <div fxLayoutAlign="end center">
            {{parsedSpin.isPayment ? 'Yes' : 'No'}}
          </div>
        </td>
      </tr>
      </tbody>
    </table>
  </div>
</div>

<div *ngIf="((!canvas && !isRedeemBns()) ||
  !(canvas &&
  (isSrt() ||
  isJackpotSrtChallenge() ||
  isJackpotSrtTournament() ||
  isRedeemBns() ||
  isPhTournament() ||
  isPrizeDrop() ||
  isMustWinJackpot() ||
  isL7Jackpot() ||
  isInstantJackpotPlay() ||
  isSharedJackpotPrize())))" style="position: relative">
  <div *ngIf="!isLoaded" class="spinner-wrapper">
    <mat-spinner [diameter]="40"></mat-spinner>
  </div>
  <div id="gameContainer" fxLayout="row">
  </div>
</div>

<div class="l7-jackpot" *ngIf="isL7Jackpot()">
  <img [src]="getL7JackpotImage()"/>
</div>

<div class="instant-jackpot" [class.large]="instantJackpotImg === getInstantJackpotPlay()" *ngIf="isInstantJackpotPlay()">
  <img [src]="getInstantJackpotPlay()"/>
</div>

<spin-jackpot
  *ngIf="canvas && !isInstantJackpotPlay()"
  [spin]="parsedSpin">
</spin-jackpot>

<spin-shared-jackpot-prize
  *ngIf="canvas"
  [spin]="parsedSpin">
</spin-shared-jackpot-prize>

<spin-bns
  *ngIf="canvas"
  [spin]="parsedSpin">
</spin-bns>

<spin-ph-tournament
  *ngIf="canvas"
  [spin]="parsedSpin">
</spin-ph-tournament>

<spin-prize-drop
  *ngIf="canvas"
  [spin]="parsedSpin">
</spin-prize-drop>

<spin-srt-canvas
  *ngIf="canvas && isSrt()"
  [spin]="spin">
</spin-srt-canvas>
