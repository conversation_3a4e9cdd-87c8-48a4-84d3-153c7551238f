import moment from 'moment';
import { BehaviorSubject } from 'rxjs';

import transformHistoryResponse from './sw_live_roulette_transform/transform';

export enum GAME_RENDERER {
  plain,
}

interface IIframeMapArray {
  [key: string]: {
    element: HTMLIFrameElement,
    ready: boolean;
  };
}

interface ISpinItem {
  [key: string]: any;
}

interface ISpinItemJackpot {
  [key: string]: any;
}

interface ISpinDetails {
  gameId?: string;
  gameCode?: string;
  details: ISpinItem | ISpinItemJackpot;
  initionalData: { [key: string]: any };
  historyInfo: { [key: string]: any };
  iframeHolderID: string;
}

export class SWGamehistory {
  private static instance: SWGamehistory;
  private static defaultConfig = {
    config: {
      language: 'en',
      dpi: 'ldpi',
      mobile: 'false',
    },
  };

  public config;
  public iWindow;
  public iframe;
  public info;
  public isReady = false;
  public isLoaded = new BehaviorSubject<boolean>(false);

  private language;
  private currency;
  private frameMapArray: IIframeMapArray = <IIframeMapArray>{};

  private readonly ghAppPluginUrl;
  private readonly windowCanvasMessageEventListener;

  static getInstance( ghAppPluginUrl: string, options? ) {
    if (!SWGamehistory.instance) {
      SWGamehistory.instance = new SWGamehistory(ghAppPluginUrl, options);
    }
    return SWGamehistory.instance;
  }

  constructor( url: string, options? ) {
    Object.keys(SWGamehistory.defaultConfig).forEach(key => {
      this[key] = SWGamehistory.defaultConfig[key];
    });
    if (options) {
      Object.keys(options).forEach(key => {
        this[key] = options[key];
      });
    }
    this.ghAppPluginUrl = url;
    this.windowCanvasMessageEventListener = this._windowCanvasMessageEventListener.bind(this);
  }

  public _windowCanvasMessageEventListener( e ) {
    setTimeout(() => {
      if (e.data.height !== undefined) {
        if (e.data.height && this.iframe.height !== e.data.height + 'px') {
          this.iframe.height = e.data.height + 'px';
        }
      }
    }, 1000);

    if (e.data.ready) {
      if (this.isReady) {
        return;
      }
      this.isReady = true;

      const data = this.info;
      const response = this.transformHistoryToInitResponse(data);
      const msg = {
        history: {
          response: response,
          showPreview: true,
          showRewards: true
        }
      };
      const targetOrigin = this.iframe.src;
      this.iWindow.postMessage(msg, targetOrigin);
    }
  }

  remove( selector: string ) {
    if (this.frameMapArray[selector]) {
      const iframe: HTMLIFrameElement = this.frameMapArray[selector].element;
      if (iframe) {
        iframe.parentNode.removeChild(iframe);
      }
      delete this.frameMapArray[selector];
    }
    window.removeEventListener('message', this.windowCanvasMessageEventListener);
    if (SWGamehistory.instance) {
      SWGamehistory.instance = undefined;
    }
    this.isReady = false;
  }

  commitData( iframeHolderID: string, data: ISpinDetails ) {
    this.info = data;
    const gameCode = data.gameId || data.gameCode;
    if (!gameCode) {
      throw new Error('Empty gameCode');
    }

    data.iframeHolderID = iframeHolderID;
    data.historyInfo.theme = this.config.theme;

    this.getContainer(iframeHolderID,
      () => {
        this.isLoaded.next(true);
      });
  }

  private getContainer( selector: string, onError ) {
    let iframe: HTMLIFrameElement;

    if (this.frameMapArray[selector]) {
      iframe = this.frameMapArray[selector].element;
    } else {
      const language = this.language || this.config.language;
      const currency = this.currency || this.config.currency;

      iframe = <HTMLIFrameElement>document.createElement('iframe');
      iframe.src = `${this.ghAppPluginUrl}?language=${language}` +
        `&mobile=${this.config.mobile}&dpi=${this.config.dpi}&t=${+new Date()}&currency=${currency}`;
      iframe.setAttribute('class', 'gh-iframe');

      iframe.onload = () => {
        this.frameMapArray[selector].ready = true;
        this.isLoaded.next(true);

        this.iWindow = (<HTMLIFrameElement>iframe).contentWindow;
        this.iframe = iframe;
        const url = this.info.historyInfo.url.replace('index.html', 'history.html');

        if (this.info.historyInfo.historyRenderType === GAME_RENDERER.plain) {
          this.isReady = true;
          const msg = {
            type: 'spin',
            spin: JSON.stringify(this.info),
            language: this.config.language,
          };
          this.iWindow.postMessage(msg, url);
        } else {
          window.addEventListener('message', this.windowCanvasMessageEventListener);
          this.iWindow.postMessage({ init: true }, url);
        }

        return;
      };
      iframe.onerror = () => onError();

      this.frameMapArray[selector] = {
        element: iframe,
        ready: false
      };

      const host = document.getElementById(selector);
      host.appendChild(iframe);
    }
  }

  // TODO remove, when working with Management API
  private transformHistoryToInitResponse( history ) {
    const data = {};
    Object.keys(history.initSettings).forEach(key => {
      data[key] = history.initSettings[key];
    });
    Object.keys(history.details).forEach(key => {
      data[key] = history.details[key];
    });

    let fullResponse = { ...history };

    if (fullResponse.insertedAt) {
      fullResponse.insertedAt = moment(fullResponse.insertedAt).utcOffset(fullResponse.insertedAt).toJSON();
    }

    if (fullResponse.firstTs) {
      fullResponse.firstTs = moment(fullResponse.firstTs).utcOffset(fullResponse.firstTs).toJSON();
    }

    const message = {
      balance: {
        currency: history.currency,
        amount: 0,
        real: { amount: 0 },
        bonus: { amount: 0 }
      },
      result: data,
      fullResponse
    };

    if (history.gameId.indexOf('sw_live_erol') !== -1) {
      message['result'] = Object.assign({}, history.initSettings, transformHistoryResponse(history.details));
    } else {
      message['result'] = Object.assign({}, history.initSettings, history.details);
    }

    return message;
  }
}
