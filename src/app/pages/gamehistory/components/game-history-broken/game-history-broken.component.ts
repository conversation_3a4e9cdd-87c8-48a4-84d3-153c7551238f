import { ComponentType } from '@angular/cdk/overlay';
import { Component, Injector, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { Params } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import {
  RowAction,
  SwHubAuthService,
  SwHubEntityItem,
  SwHubEntityService,
  SwHubShortEntity,
  SwuiGridComponent,
  SwuiGridDataService,
  SwuiGridField,
  SwuiNotificationsService,
  SwuiSelectOption,
  SwuiTopFilterDataService
} from '@skywind-group/lib-swui';
import moment from 'moment';
import { combineLatest, of, Subject, throwError } from 'rxjs';
import { catchError, delay, filter, finalize, map, switchMap, take, takeUntil, tap } from 'rxjs/operators';
import { BoConfirmationComponent } from '../../../../common/components/bo-confirmation/bo-confirmation.component';
import { EntitySettingsModel } from '../../../../common/models/entity-settings.model';
import { EntityDataSourceService } from '../../../../common/services/entity-data-source.service';
import { EntitySettingsService } from '../../../../common/services/entity-settings.service';
import { GameService } from '../../../../common/services/game.service';

import { Game, GameInfo, UnfinishedGameHistory } from '../../../../common/typings';
import { checkAndRemoveBalanceColumns } from '../internal-game-history/base-history/base-history.component';
import { IframeViewModalComponent } from '../internal-game-history/modals/iframe-view-modal/iframe-view-modal.component';
import { RoundInfoViewModalComponent } from '../internal-game-history/modals/round-info-view-modal.component';
import { GameHistoryBrokenService } from './game-history-broken.service';
import { GameHistoryBrokenSchema } from './schema';


const COMPONENT_NAME: string = 'game-history-broken';
const REQUIRED_FILTERS = ['playerCode'];

@Component({
  selector: 'game-history-broken',
  templateUrl: './game-history-broken.component.html',
  providers: [
    GameHistoryBrokenService,
    SwuiTopFilterDataService,
    { provide: SwuiGridDataService, useExisting: GameHistoryBrokenService }
  ]
})
export class GameHistoryBrokenComponent implements OnInit, OnDestroy {

  readonly componentName = COMPONENT_NAME;
  schema?: SwuiGridField[];
  schemaFilter: SwuiGridField[] = [];
  notifyVisibility = true;
  loading: boolean = false;

  actions: RowAction[] = [];
  initialFilterState = {
    firstTs__gte: moment().subtract(1, 'month'),
    firstTs__lt: moment()
  };

  idPathMap: SwHubEntityItem[];
  hideBalanceBeforeAndAfter = false;

  @ViewChild(SwuiGridComponent) grid: SwuiGridComponent<GameInfo>;

  private destroy$ = new Subject();

  private dialog: MatDialog;
  private gameHistoryBrokenService: GameHistoryBrokenService;
  private authService: SwHubAuthService;
  private entitySettingsService: EntitySettingsService<EntitySettingsModel>;
  private gameService: GameService;
  private gameHistoryService: GameHistoryBrokenService;
  private filterDataService: SwuiTopFilterDataService;
  private notifications: SwuiNotificationsService;
  private translate: TranslateService;
  private hubEntityService: SwHubEntityService;
  private entityDataSourceService: EntityDataSourceService;

  constructor( private injector: Injector ) {
    this.injectServices();

    this.hubEntityService.items$.pipe(
      takeUntil(this.destroy$)
    ).subscribe(( idPathMap ) => {
      this.idPathMap = idPathMap;
    });
  }

  ngOnInit() {
    this.entityDataSourceService.show();

    this.hubEntityService.entitySelected$
      .pipe(
        switchMap(( entity: SwHubShortEntity ) => {
          return this.entitySettingsService.getSettings(entity.path)
            .pipe(
              map(( entitySettings: EntitySettingsModel ) => {
                this.hideBalanceBeforeAndAfter = entitySettings?.hideBalanceBeforeAndAfter;
                this.initSchema();

                return entity;
              }),
            );
        }),
        switchMap(( entity: SwHubShortEntity ) => {
          return this.gameService.getAllGames(entity.path, false, true);
        }),
        map(( games: Game[] ) => {
            return games.map<SwuiSelectOption>(( { code, title } ) => ({ id: code, text: `${title} (${code})` }));
          }
        ),
        switchMap(games => combineLatest([of(games), this.filterDataService.appliedFilter]).pipe(take(1))),
        tap(( [games, filterState]: [SwuiSelectOption[], any] ) => {
          if (filterState.gameCode && !games.find(game => game.id === filterState.gameCode)) {
            this.filterDataService.submitFilter({ ...filterState, gameCode: null });
          }
        }),
        delay(10),
        takeUntil(this.destroy$)
      )
      .subscribe(( [games]: [SwuiSelectOption[], any] ) => {
        this.schemaFilter = this.schemaFilter.map(( item ) => {
          if (item.field === 'gameCode' && item.type === 'select') {
            item.data = games || [];
          }

          return item;
        });
      });

    this.filterDataService.appliedFilter
      .pipe(
        takeUntil(this.destroy$)
      ).subscribe(params => {
      this.updateOnParamsChange(params);
    });

    this.actions.push(
      new RowAction({
        icon: 'history',
        inMenu: false,
        title: 'View details',
        canActivateFn: ( info ) => info?.totalEvents > 0,
        fn: ( info ) => {
          this.hubEntityService.items$
            .pipe(
              switchMap(( entities: SwHubEntityItem[] ) => {
                const entity = entities.find(( { id } ) => id === info.brandId);
                info._meta = {
                  ...info._meta,
                  name: entity.name,
                  fullPath: entity.path,
                  hideBalanceBeforeAndAfter: this.hideBalanceBeforeAndAfter,
                };
                return this.gameService.getEntityGame(info.gameCode, entity.path);
              }),
              take(1)
            )
            .subscribe(
              data => {
                let component: ComponentType<IframeViewModalComponent | RoundInfoViewModalComponent> = data.historyRenderType === 3 ?
                  IframeViewModalComponent :
                  RoundInfoViewModalComponent;

                this.dialog.open(component, {
                  width: '1000px',
                  data: {
                    roundInfo: info
                  },
                  disableClose: true
                });
              }
            );
        },
      })
    );
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
    this.entityDataSourceService.hide();
  }

  initSchema() {
    const schema = new GameHistoryBrokenSchema(this.idPathMap);
    this.schema = checkAndRemoveBalanceColumns(schema.schemaList, this.authService, this.hideBalanceBeforeAndAfter);
    this.schemaFilter = schema.schemaFilter;
  }

  statusClick( { row, payload }: { row: UnfinishedGameHistory; payload: { status: string; onCompleteFn: () => void } } ): void {
    this.dialog.open(BoConfirmationComponent, {
      width: '500px',
      data: { message: this.getPopupMessage(row, payload) },
      disableClose: true
    }).afterClosed().pipe(
      finalize(() => payload.onCompleteFn()),
      filter(result => result),
      switchMap(() => {
        let fullPath = this.idPathMap.find(entity => entity.id === `${row.brandId}`).path || '';
        fullPath = fullPath && fullPath !== ':' ? fullPath : '';
        return this.gameHistoryService.changeStatus(row, payload.status, fullPath)
          .pipe(
            catchError(err => {
              if (err.error.code === 503 && this.authService.isSuperAdmin) {
                return this.dialog.open(BoConfirmationComponent, {
                  width: '500px',
                  data: { message: 'GAMEHISTORY.INTERNAL.ignoreMerchantParams' },
                  disableClose: true
                }).afterClosed()
                  .pipe(
                    filter(data => data),
                    switchMap(() => {
                      fullPath = this.idPathMap.find(entity => entity.id === `${row.brandId}`).path || '';
                      fullPath = fullPath && fullPath !== ':' ? fullPath : '';
                      return this.gameHistoryService.changeStatus(row, payload.status, fullPath, true);
                    }),
                  );
              }
              return throwError(err);
            }),
          );
      }),
      switchMap(data => {
        switch (data['result']) {
          case 'force-finished':
            return this.translate.get([
              'GAMEHISTORY.INTERNAL.NOTIFICATIONS.roundFinished',
              'GAMEHISTORY.INTERNAL.NOTIFICATIONS.noForceFinish',
              'GAMEHISTORY.INTERNAL.NOTIFICATIONS.note'
            ]);
          case 'finalized':
            return this.translate.get(['GAMEHISTORY.INTERNAL.NOTIFICATIONS.roundFinalized']);
          default:
            return this.translate.get(['GAMEHISTORY.INTERNAL.NOTIFICATIONS.roundClosed']);
        }
      }))
      .subscribe(( mes ) => {
        const messages = Object.values(mes)
          .reduce<string>(( res: string, item: string ) => {
            if (res) {
              res = `${res}. ${item}`;
            } else {
              res = item;
            }

            return res;
          }, '');

        this.notifications.success(messages, '');
        this.grid.dataSource.loadData();
      });
  }

  downloadCsv() {
    this.loading = true;
    this.filterDataService.appliedFilter.pipe(
      switchMap(( params ) => {
        const addParams = Object.entries(params).reduce(( res, [key, value] ) => {
          if (value) {
            res[key] = value;
          }

          return res;
        }, {});
        if ('path' in addParams) {
          delete addParams['path'];
        }
        return this.gameHistoryBrokenService.downloadCsv(addParams);
      }),
      catchError(( err ) => {
        this.loading = false;
        return throwError(err);
      }),
      takeUntil(this.destroy$)
    ).subscribe(() => this.loading = false);
  }

  exportPage() {
    this.gameHistoryBrokenService.exportPage(this.grid.dataSource.data, this.grid.displayedColumns, this.grid.paginator.pageIndex + 1);
  }

  private getPopupMessage( row: UnfinishedGameHistory, payload: { status: string; } ): string {
    switch (payload.status) {
      case 'forceFinish':
        if (row.status === 'broken') {
          return 'GAMEHISTORY.INTERNAL.messageForceFinishBroken';
        } else {
          return 'GAMEHISTORY.INTERNAL.messageForceFinish';
        }
      case 'revert':
        if (row.status === 'broken') {
          return 'GAMEHISTORY.INTERNAL.messageRevertBroken';
        } else {
          return 'GAMEHISTORY.INTERNAL.messageRevert';
        }
      case 'finalize':
        return 'GAMEHISTORY.INTERNAL.messageFinalize';
      case 'retryPending':
        return 'GAMEHISTORY.INTERNAL.messageRetry';
      case 'requireTransferOut':
        return 'GAMEHISTORY.INTERNAL.messageTransferOut';
      case 'manualFinalize':
        return 'GAMEHISTORY.INTERNAL.messageManualFinalize';
      default:
        break;
    }
  }

  private updateOnParamsChange( params: Params ) {
    this.notifyVisibility = !REQUIRED_FILTERS.some(field => !!params[field]);

    if (this.notifyVisibility) {
      this.gameHistoryBrokenService.blockData();
    } else {
      this.gameHistoryBrokenService.unblockData();
    }
  }

  private injectServices() {
    this.dialog = this.injector.get<MatDialog>(MatDialog);
    this.gameHistoryBrokenService = this.injector.get<GameHistoryBrokenService>(GameHistoryBrokenService);
    this.authService = this.injector.get<SwHubAuthService>(SwHubAuthService);
    this.gameService = this.injector.get<GameService>(GameService);
    this.gameHistoryService = this.injector.get<GameHistoryBrokenService>(GameHistoryBrokenService);
    this.filterDataService = this.injector.get<SwuiTopFilterDataService>(SwuiTopFilterDataService);
    this.notifications = this.injector.get<SwuiNotificationsService>(SwuiNotificationsService);
    this.translate = this.injector.get<TranslateService>(TranslateService);
    this.hubEntityService = this.injector.get<SwHubEntityService>(SwHubEntityService);
    this.entityDataSourceService = this.injector.get<EntityDataSourceService>(EntityDataSourceService);
    this.entitySettingsService = this.injector.get<EntitySettingsService<EntitySettingsModel>>(EntitySettingsService);
  }
}
