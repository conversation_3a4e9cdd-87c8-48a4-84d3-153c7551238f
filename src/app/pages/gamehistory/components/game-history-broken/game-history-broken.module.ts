import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FlexModule } from '@angular/flex-layout';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogModule } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiGridModule, SwuiSchemaTopFilterModule } from '@skywind-group/lib-swui';

import { BoConfirmationModule } from '../../../../common/components/bo-confirmation/bo-confirmation.module';
import { DownloadCsvModule } from '../../../../common/components/download-csv/download-csv.module';
import { GameService } from '../../../../common/services/game.service';
import { GameNotifyModule } from '../game-notify/game-notify.module';
import { IframeViewModalComponent } from '../internal-game-history/modals/iframe-view-modal/iframe-view-modal.component';
import { IframeViewModalModule } from '../internal-game-history/modals/iframe-view-modal/iframe-view-modal.module';
import { GameHistoryBrokenComponent } from './game-history-broken.component';
import { GameHistoryBrokenService } from './game-history-broken.service';


@NgModule({
  declarations: [
    GameHistoryBrokenComponent
  ],
  exports: [
    GameHistoryBrokenComponent
  ],
    imports: [
        CommonModule,
        SwuiGridModule,
        FlexModule,
        MatIconModule,
        MatButtonModule,
        MatDialogModule,
        TranslateModule,
        BoConfirmationModule,
        GameNotifyModule,
        MatTooltipModule,
        SwuiSchemaTopFilterModule,
        IframeViewModalModule,
        MatProgressSpinnerModule,
        DownloadCsvModule,
    ],
  providers: [
    GameHistoryBrokenService,
    GameService,
  ],
  entryComponents: [
    IframeViewModalComponent,
  ],
})
export class GameHistoryBrokenModule {
}
