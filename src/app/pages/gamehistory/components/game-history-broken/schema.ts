import { SchemaFilter<PERSON>atchEnum, SwHubEntityItem, SwuiGridField } from '@skywind-group/lib-swui';
import {
  ACCOUNT_TYPE_CLASS_MAP,
  ACCOUNT_TYPE_MAP,
  CURRENCY_LIST$,
  DEVICE_MAP,
  FINISHED_CLASS_MAP,
  FINISHED_MAP,
  FINISHED_OPTIONS_LIST,
  IS_TEST_MAP,
  OUTCOME_MAP,
  STATUS_MAP,
} from '../../../../app.constants';

import { CurrencyModel } from '../../../../common/models/currency.model';
import { FormattedMoneyPipe } from '../../../../common/pipes/formatted-money/formatted-money.pipe';
import { GameHistory } from '../../../../common/typings/reports/game_history';
import { PagesRoutes } from '../../../pages.menu';
import { getCurrencyDivider, transformCurrencyItem, transformFormatCurrencyValue } from '../../../../common/core/currecy-transform';
import { map } from 'rxjs/operators';


export const buildSchema = (entityItems: SwHubEntityItem[]): SwuiGridField[] => {
  return [
    {
      field: 'brandId',
      title: 'GAMEHISTORY.GRID.operator',
      type: 'string',
      isList: true,
      isViewable: true,
      isSortable: false,
      isFilterable: false,
      td: {
        type: 'calc',
        titleFn: (row) => entityItems?.find(entity => entity.id === row.brandId)?.name,
        classFn: () => {
        },
      },
    },
    {
      field: 'playerCode',
      title: 'GAMEHISTORY.GRID.playerCode',
      type: 'string',
      isList: true,
      isViewable: true,
      isSortable: false,
      isFilterable: true,
      isFilterableAlways: true,
      td: {
        type: 'link',
        data: [],
        isDisabled: (row) => {
          const entity = entityItems.find(({id}) => id === row.brandId);
          return entity?.type === 'merchant';
        },
        linkFn: (row) => {
          const fullPath = entityItems.find(entity => entity.id === row.brandId).path;

          let url;
          if (fullPath && fullPath !== ':') {
            row._meta = {fullPath: fullPath};
            url = `/pages/${PagesRoutes.Customers}/${fullPath}/${row.playerCode}`;
          } else {
            url = `/pages/${PagesRoutes.Customers}/${row.playerCode}`;
          }
          return [url];
        },
        titleFn: (row) => row.playerCode,
      },
      filterMatch: SchemaFilterMatchEnum.Equals,
      alignment: {
        th: 'left',
        td: 'left',
      },
    },
    {
      field: 'roundId',
      title: 'GAMEHISTORY.GRID.roundId',
      type: 'string',
      isList: true,
      isViewable: true,
      isSortable: false,
      isFilterable: false,
      filterMatch: SchemaFilterMatchEnum.In,
      alignment: {
        th: 'left',
        td: 'left',
      },
    },
    {
      field: 'gameNameLabel',
      title: 'GAMEHISTORY.GRID.gameNameLabel',
      type: 'string',
      isList: true,
      td: {
        type: 'string',
        nowrap: true
      },
      alignment: {
        th: 'left',
        td: 'left',
      },
      isFilterable: false
    },
    {
      field: 'gameCode',
      title: 'GAMEHISTORY.GRID.gameNameLabel',
      type: 'select',
      isList: false,
      isFilterable: true,
      data: [],
      td: {type: 'string'},
      showSearch: true
    },
    {
      field: 'gameCode',
      title: 'GAMEHISTORY.GRID.gameCode',
      type: 'boChoice',
      data: [],
      isList: true,
      isViewable: true,
      isSortable: false,
      isFilterable: false,
      filter: {
        showSearch: true,
        title: 'GAMEHISTORY.GRID.gameNameLabel',
      },
      filterMatch: SchemaFilterMatchEnum.Equals,
      td: {type: 'string'},
      alignment: {
        th: 'left',
        td: 'left',
      },
    },
    {
      field: 'finished',
      title: 'GAMEHISTORY.GRID.isFinished',
      type: 'select',
      data: FINISHED_MAP.map(({id, displayName}) => ({id, text: displayName})),
      isList: true,
      isViewable: true,
      isSortable: false,
      isFilterable: false,
      td: {
        type: 'brokenGameFinish',
        optionsList: FINISHED_OPTIONS_LIST,
        titleFn: (row, schema) => {
          const value = row[schema.field];
          return value ? 'GAMEHISTORY.GRID.isFinished' : 'GAMEHISTORY.GRID.unfinished';
        },
        classFn: (row: any, schema: SwuiGridField) => FINISHED_CLASS_MAP[(row && row[schema.field])],
      },
      filter: {
        field: 'finished'
      },
      alignment: {
        th: 'center',
        td: 'center',
      },
    },
    {
      field: 'status',
      title: 'GAMEHISTORY.GRID.status',
      type: 'select',
      data: STATUS_MAP,
      isList: true,
      isViewable: true,
      isSortable: false,
      isFilterable: true,
      td: {
        type: 'calc',
        titleFn: (row, schema) => {
          if (STATUS_MAP.find(item => row[schema.field] === item.id)) {
            return STATUS_MAP.find(item => row[schema.field] === item.id).title;
          } else {
            return row[schema.field];
          }
        },
        classFn: () => '',
      },
      alignment: {
        th: 'center',
        td: 'center',
      },
      emptyOption: {
        show: true,
        placeholder: '- All -'
      },
    },
    {
      field: 'device',
      title: 'GAMEHISTORY.GRID.device',
      type: 'select',
      data: DEVICE_MAP,
      isList: true,
      isViewable: true,
      isSortable: false,
      isFilterable: false,
      filter: {
        field: 'device',
        type: 'select',
      },
      emptyOption: {
        show: false,
      },
      td: {
        type: 'string'
      },
      alignment: {
        th: 'center',
        td: 'center',
      },
    },
    {
      field: 'isTest',
      title: 'GAMEHISTORY.GRID.account_type',
      type: 'select',
      data: IS_TEST_MAP,
      isList: true,
      isViewable: true,
      isSortable: false,
      isFilterable: false,
      td: {
        type: 'calc',
        titleFn: (row: any, schema: SwuiGridField) => ACCOUNT_TYPE_MAP[row && row[schema.field]],
        classFn: (row: any, schema: SwuiGridField) => ACCOUNT_TYPE_CLASS_MAP[(row && row[schema.field])],
      },
      filter: {
        field: 'isTest',
        type: 'select',
      },
      alignment: {
        th: 'center',
        td: 'center',
      },
    },
    {
      field: 'firstTs',
      title: 'GAMEHISTORY.GRID.firstTs',
      type: 'datetimerange',
      isList: true,
      isViewable: true,
      isSortable: true,
      isFilterable: true,
      filterMatch: {
        from: SchemaFilterMatchEnum.GreaterThanEquals,
        to: SchemaFilterMatchEnum.LessThan,
      },
      config: {
        timePicker: true,
        chooseStart: true,
      },
      td: {
        type: 'timestamp',
        nowrap: true
      },
      alignment: {
        th: 'right',
        td: 'right',
      },
    },
    {
      field: 'ts',
      title: 'GAMEHISTORY.GRID.ts',
      type: 'datetimerange',
      isList: true,
      isViewable: true,
      isSortable: false,
      isFilterable: false,
      filterMatch: {
        from: SchemaFilterMatchEnum.GreaterThanEquals,
        to: SchemaFilterMatchEnum.LessThan,
      },
      config: {
        timePicker: true,
        chooseStart: true,
      },
      td: {
        type: 'timestamp',
        nowrap: true
      },
      alignment: {
        th: 'right',
        td: 'right',
      },
    },
    {
      field: 'currency',
      title: 'GAMEHISTORY.GRID.currency',
      type: 'select',
      isList: true,
      isViewable: true,
      isSortable: false,
      isFilterable: false,
      data: CURRENCY_LIST$.pipe(map(curr => curr.map((c: any) => (new CurrencyModel(c)).toSelectOption()))),
      placeholder: 'USD, EUR...',
      scope: 'ALL.DEFAULT_SCHEMA_SECTIONS.financial',
      alignment: {
        th: 'center',
        td: 'center',
      },
      td: {
        type: 'calc',
        titleFn: row => transformCurrencyItem(0, row.currency).label
      }
    },
    {
      field: 'balanceBefore',
      title: 'GAMEHISTORY.GRID.balanceBefore',
      type: 'numericrange',
      isList: true,
      isViewable: true,
      isSortable: false,
      isFilterable: false,
      dependsOn: 'currency',
      dependsOnFieldName: 'GAMEHISTORY.GRID.currency',
      getDivider: getCurrencyDivider,
      scope: 'ALL.DEFAULT_SCHEMA_SECTIONS.financial',
      td: {
        type: 'calc',
        titleFn: row => transformFormatCurrencyValue(row.balanceBefore, row.currency)
      },
      alignment: {
        th: 'right',
        td: 'right',
      },
    },
    {
      field: 'bet',
      title: 'GAMEHISTORY.GRID.bet',
      type: 'numericrange',
      isList: true,
      isViewable: true,
      isSortable: false,
      isFilterable: false,
      dependsOn: 'currency',
      dependsOnFieldName: 'GAMEHISTORY.GRID.currency',
      getDivider: getCurrencyDivider,
      filterMatch: {
        from: SchemaFilterMatchEnum.GreaterThanEquals,
        to: SchemaFilterMatchEnum.LessThanEquals,
      },
      scope: 'ALL.DEFAULT_SCHEMA_SECTIONS.financial',
      td: {
        type: 'calc',
        titleFn: row => transformFormatCurrencyValue(row.bet, row.currency)
      },
      alignment: {
        th: 'right',
        td: 'right',
      },
    },
    {
      field: 'win',
      title: 'GAMEHISTORY.GRID.win',
      type: 'numericrange',
      isList: true,
      isViewable: true,
      isSortable: true,
      isFilterable: false,
      dependsOn: 'currency',
      dependsOnFieldName: 'GAMEHISTORY.GRID.currency',
      getDivider: getCurrencyDivider,
      filterMatch: {
        from: SchemaFilterMatchEnum.GreaterThanEquals,
        to: SchemaFilterMatchEnum.LessThanEquals,
      },
      scope: 'ALL.DEFAULT_SCHEMA_SECTIONS.financial',
      td: {
        type: 'calc',
        titleFn: row => transformFormatCurrencyValue(row.win, row.currency)
      },
      alignment: {
        th: 'right',
        td: 'right',
      },
    },
    {
      field: 'credit',
      title: 'GAMEHISTORY.GRID.credit',
      type: 'string',
      isList: true,
      isViewable: true,
      isSortable: false,
      isFilterable: false,
      td: {
        type: 'calc',
        titleFn: row => transformFormatCurrencyValue(row.credit, row.currency)
      },
      alignment: {
        th: 'right',
        td: 'right',
      },
    },
    {
      field: 'debit',
      title: 'GAMEHISTORY.GRID.debit',
      type: 'string',
      isList: true,
      isViewable: true,
      isSortable: false,
      isFilterable: false,
      td: {
        type: 'calc',
        titleFn: row => transformFormatCurrencyValue(row.debit, row.currency)
      },
      alignment: {
        th: 'right',
        td: 'right',
      },
    },
    {
      field: 'revenue',
      title: 'GAMEHISTORY.GRID.revenue',
      type: 'numericrange',
      isList: true,
      isViewable: true,
      isSortable: false,
      isFilterable: false,
      dependsOn: 'currency',
      dependsOnFieldName: 'GAMEHISTORY.GRID.currency',
      getDivider: getCurrencyDivider,
      filterMatch: {
        from: SchemaFilterMatchEnum.GreaterThanEquals,
        to: SchemaFilterMatchEnum.LessThanEquals,
      },
      scope: 'ALL.DEFAULT_SCHEMA_SECTIONS.financial',
      td: {
        type: 'calc',
        titleFn: row => transformFormatCurrencyValue(row.revenue, row.currency),
        classFn: (row: GameHistory, schema: SwuiGridField) => ({
          'text-green': row[schema.field] > 0,
          'text-muted': row[schema.field] === '0.00',
          'text-danger': row[schema.field] < 0,
          'pull-right': true,
          'nowrap': true
        }),
      },
      alignment: {
        th: 'right',
        td: 'right',
      },
    },
    {
      field: 'balanceAfter',
      title: 'GAMEHISTORY.GRID.balanceAfter',
      type: 'numericrange',
      isList: true,
      isViewable: true,
      isSortable: false,
      isFilterable: false,
      dependsOn: 'currency',
      dependsOnFieldName: 'GAMEHISTORY.GRID.currency',
      getDivider: getCurrencyDivider,
      scope: 'ALL.DEFAULT_SCHEMA_SECTIONS.financial',
      td: {
        type: 'calc',
        titleFn: row => transformFormatCurrencyValue(row.balanceAfter, row.currency)
      },
      alignment: {
        th: 'right',
        td: 'right',
      },
    },
    {
      field: 'outcome',
      title: 'GAMEHISTORY.GRID.outcome',
      type: 'calc',
      isList: true,
      isViewable: false,
      isSortable: false,
      isFilterable: false,
      td: {
        type: 'calc',
        titleFn: (row, schema) => {
          const outcome = OUTCOME_MAP.find(item => row[schema.field] === item.id);
          return outcome ? outcome.title : row[schema.field];
        },
        classFn: (row, schema) => {
          const outcome = OUTCOME_MAP.find(item => row[schema.field] === item.id);
          return outcome ? outcome.class : row[schema.field];
        },
      },
      alignment: {
        th: 'right',
        td: 'right',
      },
    },
    {
      field: 'ggrPerc',
      title: 'GAMEHISTORY.GRID.ggrPerc',
      type: 'string',
      isList: true,
      isViewable: false,
      isSortable: false,
      isFilterable: false,
      td: {
        type: 'currency',
        titleFn: ({ggrPerc}) => new FormattedMoneyPipe().transform(ggrPerc || 0, 2),
      },
      alignment: {
        th: 'right',
        td: 'right',
      },
    },
    {
      field: 'totalJpWin',
      title: 'GAMEHISTORY.GRID.totalJpWin',
      type: 'number',
      isList: true,
      isViewable: false,
      isSortable: false,
      isFilterable: false,
      td: {
        type: 'calc',
        titleFn: row => transformFormatCurrencyValue(row.totalJpWin, row.currency)
      },
      alignment: {
        th: 'right',
        td: 'right',
      },
    },
  ];
};

export class GameHistoryBrokenSchema {
  schemaList: SwuiGridField[] = [];
  schemaFilter: SwuiGridField[] = [];
  private schema: SwuiGridField[] = [];

  constructor(entityItems: SwHubEntityItem[]) {
    this.schema = buildSchema(entityItems);
    this.schemaList = this.schema.filter(el => el.isList);
    this.schemaFilter = this.schema.filter(el => el.isFilterable);
  }
}
