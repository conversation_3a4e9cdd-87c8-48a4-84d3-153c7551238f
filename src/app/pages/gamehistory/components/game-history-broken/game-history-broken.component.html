<game-notify *ngIf="notifyVisibility" message="GAMEHISTORY.INTERNAL.notifyUnfinished"></game-notify>

<div class="sw-grid-layout" style="margin-top: 32px">
  <div class="sw-grid-layout__table" *ngIf="schema">
    <lib-swui-schema-top-filter [schema]="schemaFilter"></lib-swui-schema-top-filter>
    <lib-swui-grid #grid
                   class="history__grid"
                   [schema]="schema"
                   [columnsManagement]="true"
                   (widgetActionEmitted)="statusClick($event)"
                   [rowActions]="actions"
                   [useHubEntity]="true"
                   [rowActionsColumnTitle]="'Actions'"
                   [savedFilteredPageName]="'unfinished'"
                   [savedFilteredPageParams]=initialFilterState
                   [blindPaginator]="true"
                   [ignorePlainLink]="true"
                   [gridId]="componentName">
      <download-csv [loading]="loading" (downloadCsv)="downloadCsv()"></download-csv>

      <button mat-icon-button matTooltip="Export current page" (click)="exportPage()">
        <mat-icon>archive</mat-icon>
      </button>
    </lib-swui-grid>
  </div>
</div>
