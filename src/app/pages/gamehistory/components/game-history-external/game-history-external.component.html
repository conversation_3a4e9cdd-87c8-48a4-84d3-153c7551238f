<lib-swui-page-panel [title]="'MENU_SECTIONS.externalGamesHistory'">
</lib-swui-page-panel>
<div class="p-32 sw-grid-layout">
  <div class="sw-grid-layout__table">
    <lib-swui-schema-top-filter [schema]="schemaFilter"></lib-swui-schema-top-filter>
    <lib-swui-grid class="history__grid"
                   [schema]="schema"
                   [columnsManagement]="true"
                   [useHubEntity]="true"
                   [ignorePlainLink]="true"
                   [rowActionsColumnTitle]="'Actions'"
                   [rowActions]="actions"
                   [savedFilteredPageName]="'external-game-history'"
                   [gridId]="componentName">
      <download-csv [loading]="loading" (downloadCsv)="downloadCsv()"></download-csv>
      <button mat-icon-button matTooltip="Export current page" (click)="exportPage()">
        <mat-icon>archive</mat-icon>
      </button>
    </lib-swui-grid>
  </div>
</div>
