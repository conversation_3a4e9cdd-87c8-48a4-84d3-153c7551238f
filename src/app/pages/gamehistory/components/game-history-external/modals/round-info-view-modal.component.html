<div class="info-header">
  <div class="info-header__logo">
    <img [src]="noImage" alt>
  </div>
  <div class="info-header__body">
    <div class="info-header__row">
          <span class="info-header__title">
              <span>{{roundInfo.gameNameLabel}}</span>
              <span>({{roundInfo.gameCode}})</span>
          </span>
      <span class="info-header__chip sw-chip" [ngClass]="{'sw-chip-green': roundInfo.finished}">
              {{(roundInfo.finished ? 'GAMEHISTORY.GRID.isFinished' : 'GAMEHISTORY.GRID.unfinished') | translate}}
            </span>
      <span *ngIf="roundInfo.isTest" class="info-header__chip sw-chip">{{'GAMEHISTORY.GRID.isTest' | translate}}</span>
    </div>
    <div class="info-header__row">
      <div class="info-header__item">
        <span class="info-header__label">{{'GAMEHISTORY.GRID.roundId' | translate}}:</span>
        <span class="info-header__value">{{roundInfo.roundId}}</span>
      </div>
      <div class="info-header__item" *ngIf="roundInfo?.extraData?.extRoundId">
        <span class="info-header__label">{{ 'GAMEHISTORY.GRID.extRoundId' | translate }}:</span>
        <span class="info-header__value">{{ roundInfo?.extraData?.extRoundId }}</span>
      </div>
      <div class="info-header__item">
        <span class="info-header__label">{{'GAMEHISTORY.GRID.playerCode' | translate}}:</span>
        <span class="info-header__value">{{roundInfo.playerCode}}</span>
      </div>
    </div>
  </div>
</div>

<div mat-dialog-content>
  <div *ngIf="url">
    <div id="frame-container">
      <iframe #iframe></iframe>
    </div>
  </div>

  <div *ngIf="!url"
       [innerHtml]="details">
  </div>
</div>

<mat-dialog-actions align="end">
  <button mat-button
          color="primary"
          class="mat-button-md"
          mat-dialog-close>
    {{ 'DIALOG.close' | translate }}
  </button>
</mat-dialog-actions>
