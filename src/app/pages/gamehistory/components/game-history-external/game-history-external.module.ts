import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FlexModule } from '@angular/flex-layout';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogModule } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiGridModule, SwuiPagePanelModule, SwuiSchemaTopFilterModule } from '@skywind-group/lib-swui';
import { DownloadCsvModule } from '../../../../common/components/download-csv/download-csv.module';
import { GameHistoryExternalComponent } from './game-history-external.component';
import { ExternalRoundInfoViewModalComponent } from './modals/round-info-view-modal.component';

@NgModule({
  declarations: [
    GameHistoryExternalComponent,
    ExternalRoundInfoViewModalComponent
  ],
  exports: [
    GameHistoryExternalComponent,
    ExternalRoundInfoViewModalComponent
  ],
    imports: [
        CommonModule,
        SwuiGridModule,
        SwuiPagePanelModule,
        FlexModule,
        MatButtonModule,
        MatIconModule,
        TranslateModule,
        MatTooltipModule,
        SwuiSchemaTopFilterModule,
        MatDialogModule,
        DownloadCsvModule
    ],
  providers: []
})
export class GameHistoryExternalModule {
}
