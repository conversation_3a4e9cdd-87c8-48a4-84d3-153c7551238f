import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { GridRequestData, SettingsService, SwuiGridDataService, SwuiNotificationsService } from '@skywind-group/lib-swui';
import * as moment from 'moment';
import { Observable, ReplaySubject, throwError } from 'rxjs';
import { catchError, filter, map, switchMap, take, tap } from 'rxjs/operators';

import { API_ENDPOINT, FORMAT_DATETIME } from '../../../../app.constants';
import { CsvSchema, CsvService, transformDate } from '../../../../common/services/csv.service';
import { GameService } from '../../../../common/services/game.service';
import { ExternalGameHistory, Game } from '../../../../common/typings';
import { GameHistory } from '../../../../common/typings/reports/game_history';
import { transformCurrencyItem, transformFormatCurrencyValue } from '../../../../common/core/currecy-transform';

const csvSchema = ( timezoneName: string, format: string, games?: Record<string, any> ): CsvSchema[] => [
  {
    name: 'extTrxId',
    title: 'GAMEHISTORY.CSV.extTrxId',
  },
  {
    name: 'gameProviderCode',
    title: 'GAMEHISTORY.CSV.gameProviderCode',
  },
  {
    name: 'playerCode',
    title: 'GAMEHISTORY.CSV.playerCode',
  },
  {
    name: 'roundId',
    title: 'GAMEHISTORY.CSV.roundId',
  },
  {
    name: 'gameNameLabel',
    title: 'GAMEHISTORY.CSV.gameName',
    transform( value: GameHistory['gameNameLabel'], rows: GameHistory ): string {
      if (!games) {
        return value;
      }

      value = value ?? games[rows.gameCode]?.title ?? '';
      value = value.replace(/"/g, "'");
      return `"${value}"`;
    }
  },
  {
    name: 'gameCode',
    title: 'GAMEHISTORY.CSV.gameCode'
  },
  {
    name: 'isTest',
    title: 'GAMEHISTORY.CSV.type',
    transform( value: ExternalGameHistory['isTest'] ) {
      return value?.toString() === 'true' ? 'Test' : 'Real';
    }
  },
  {
    name: 'insertedAt',
    title: 'GAMEHISTORY.CSV.insertedAt',
    transform: transformDate(timezoneName, format),
  },
  {
    name: 'currency',
    title: 'GAMEHISTORY.CSV.currency',
    transform(data: string): string {
      return transformCurrencyItem(0, data).label;
    }
  },
  {
    name: 'balanceBefore',
    title: 'GAMEHISTORY.CSV.balanceBefore',
    transform(data: string, rows: Record<string, any>): string {
      return transformFormatCurrencyValue(Number(data), rows.currency);
    }
  },
  {
    name: 'bet',
    title: 'GAMEHISTORY.CSV.bet',
    transform(data: string, rows: Record<string, any>): string {
      return transformFormatCurrencyValue(Number(data), rows.currency);
    }
  },
  {
    name: 'win',
    title: 'GAMEHISTORY.CSV.win',
    transform(data: string, rows: Record<string, any>): string {
      return transformFormatCurrencyValue(Number(data), rows.currency);
    }
  },
  {
    name: 'revenue',
    title: 'GAMEHISTORY.CSV.revenue',
    transform(data: string, rows: Record<string, any>): string {
      return transformFormatCurrencyValue(Number(data), rows.currency);
    }
  },
  {
    name: 'balanceAfter',
    title: 'GAMEHISTORY.CSV.balanceAfter',
    transform(data: string, rows: Record<string, any>): string {
      return transformFormatCurrencyValue(Number(data), rows.currency);
    }
  },
  {
    name: 'revenue',
    title: 'GAMEHISTORY.CSV.outcome',
    transform( value: ExternalGameHistory['revenue'] ): string {
      const amount = parseFloat(String(value ?? 0));
      if (amount > 0) {
        return 'LOSE';
      }
      if (amount < 0) {
        return 'WIN';
      }
      return 'TIE';
    }
  },
  {
    name: 'revenue',
    title: 'GAMEHISTORY.CSV.ggr',
    transform( value: ExternalGameHistory['revenue'], rows: ExternalGameHistory ): string {
      const bet = parseFloat(String(rows.bet ?? 0));
      if (!bet) {
        return '0.00';
      }
      return transformFormatCurrencyValue(((value ?? 0) / bet) * 100, '');
    }
  }
];

function getUrl( path?: string, urlList: string = '/history/external' ): string {
  const resPath = path === ':' ? '' : path;
  return `${API_ENDPOINT}${resPath ? '/entities/' + resPath : ''}${urlList}`;
}

@Injectable()
export class GameHistoryExternalService implements SwuiGridDataService<ExternalGameHistory> {
  constructor( private readonly http: HttpClient,
               private csvService: CsvService,
               private gameService: GameService,
               private setting: SettingsService,
               private readonly notifications: SwuiNotificationsService,
  ) {
  }

  getGridData( params: HttpParams, requestData?: GridRequestData ) {
    const path = requestData?.path;
    return this.http.get<ExternalGameHistory[]>(getUrl(path), {
      params,
      observe: 'response'
    }).pipe(
      tap(resp => resp.body.forEach(( player: ExternalGameHistory ) => player._meta = { fullPath: path })),
      tap(response => this.processRecord(response.body))
    );
  }

  getGames(): ReplaySubject<Game[]> {
    return this.gameService._allGames;
  }

  downloadCsv( addParams: any = {}, forcePath = '' ): Observable<any> {
    const { appSettings: { dateFormat, timeFormat, timezoneName } } = this.setting;
    const datetimeFormat = dateFormat && timeFormat ? `${dateFormat} ${timeFormat}` : FORMAT_DATETIME;
    const fileName = `Export External game ${moment().format('YYYY-MM-DD HH:MM')}`;
    return this.gameService.getAllGames(forcePath, false, true)
      .pipe(
        map<Game[], Record<string, Game>>(games => games.reduce((result, game) => ({
          ...result,
          [game.code]: game
        }), {})),
        switchMap(games => this.csvService.download(getUrl, csvSchema(timezoneName, datetimeFormat, games), fileName, addParams, forcePath))
      );
  }

  exportPage( data: Record<string, any>[], columns: string[], page: number ) {
    const { appSettings: { dateFormat, timeFormat, timezoneName } } = this.setting;
    const datetimeFormat = dateFormat && timeFormat ? `${dateFormat} ${timeFormat}` : FORMAT_DATETIME;
    const fileName = `Export External game ${moment().format('YYYY-MM-DD HH:MM')} (page ${page})`;
    this.csvService.exportToCsv(csvSchema(timezoneName, datetimeFormat), data, fileName, columns);
  }

  forceFinish( path: string, { roundId, gameCode }: ExternalGameHistory ) {
    const url = `${getUrl(path)}/game/${roundId}/forcefinish`;
    return this.http.post(url, null, {params: {gameCode, ignoreMerchantParams: true}})
      .pipe(
        catchError(err => this.handleErrors.call(this, err))
      );
  }

  finalize( path: string, { playerCode, gameCode }: ExternalGameHistory ) {
    const url = `${getUrl(path)}/game/recovery/finalize`;
    return this.http.post(url, null, {params: {gameCode, playerCode}})
      .pipe(
        catchError(err => this.handleErrors.call(this, err))
      );
  }

  private handleErrors( err ): Observable<never> {
    if (err.error) {
      this.notifications.error(err.error.message);
    } else {
      this.notifications.error(err.statusText, `Status: ${err.status}`);
    }

    return throwError(err);
  }

  private processRecord( gameHistory: ExternalGameHistory[] ) {
    return this.getGames().pipe(
      filter(( games: Game[] ) => !!games),
      map(( games: Game[] ) => {
        return gameHistory.reduce(( acc: ExternalGameHistory[], cur: ExternalGameHistory ) => {
          cur.gameNameLabel = games.find(( game: Game ) => game.code === cur.gameCode)?.title ?? '';
          cur.recoveryType = cur.recoveryType !== null ? cur.recoveryType : null;
          cur.outcome = cur.bet > cur.win ? 'LOSE' :
            (cur.bet === cur.win ? (cur.recoveryType === 'revert' ? 'VOID' : 'TIE') : 'WIN');
          if (cur.bet !== 0) {
            cur.ggrPerc = (cur.revenue / cur.bet * 100).toFixed(2);
          }

          acc.push(cur);

          return acc;
        }, []);
      }),
      take(1)
    ).subscribe();
  }
}
