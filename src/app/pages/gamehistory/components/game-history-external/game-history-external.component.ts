import { Component, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { DomSanitizer } from '@angular/platform-browser';
import {
  RowAction,
  SwHubAuthService,
  SwHubEntityService,
  SwHubShortEntity,
  SwuiGridComponent,
  SwuiGridDataService,
  SwuiGridField,
  SwuiNotificationsService,
  SwuiSelectOption,
  SwuiTopFilterDataService
} from '@skywind-group/lib-swui';
import { Subject, throwError } from 'rxjs';
import { catchError, filter, map, skip, switchMap, take, takeUntil } from 'rxjs/operators';
import { CsvService } from '../../../../common/services/csv.service';
import { EntityDataSourceService } from '../../../../common/services/entity-data-source.service';
import { GameService } from '../../../../common/services/game.service';
import { GameHistoryService } from '../../../../common/services/reports/gamehistory.service';

import { Game, GameHistory, GameInfo } from '../../../../common/typings';
import { GameHistoryExternalService } from './game-history-external.service';
import { ExternalRoundInfoViewModalComponent } from './modals/round-info-view-modal.component';
import { ExternalGameHistorySchema } from './schema';
import { PERMISSIONS_NAMES } from '../../../../app.constants';
import { BoConfirmationComponent } from '../../../../common/components/bo-confirmation/bo-confirmation.component';
import { TranslateService } from '@ngx-translate/core';


const COMPONENT_NAME: string = 'report-external-game-history';

@Component({
  selector: 'game-history-external',
  templateUrl: './game-history-external.component.html',
  providers: [
    CsvService,
    GameHistoryExternalService,
    SwuiTopFilterDataService,
    { provide: SwuiGridDataService, useExisting: GameHistoryExternalService }
  ]
})
export class GameHistoryExternalComponent {
  readonly componentName = COMPONENT_NAME;
  schema: SwuiGridField[] = [];
  schemaFilter: SwuiGridField[] = [];
  actions: RowAction[] = [];
  loading: boolean = false;
  isRoot: boolean = false;
  path: string;

  @ViewChild(SwuiGridComponent) grid: SwuiGridComponent<GameInfo>;
  private destroy$ = new Subject();

  constructor( private dialog: MatDialog,
               private readonly service: GameHistoryExternalService,
               private readonly gameHistoryGameHistoryService: GameHistoryService<GameHistory>,
               private readonly gameService: GameService,
               private readonly hubEntityService: SwHubEntityService,
               private readonly filterDataService: SwuiTopFilterDataService,
               private readonly entityDataSourceService: EntityDataSourceService,
               private sanitizer: DomSanitizer,
               private authService: SwHubAuthService,
               private readonly notifications: SwuiNotificationsService,
               private readonly translate: TranslateService
  ) {
    hubEntityService.items$.pipe(
      takeUntil(this.destroy$)
    ).subscribe(( idPathMap ) => {
      const { schemaFilter, schemaList } = new ExternalGameHistorySchema(idPathMap);
      this.schema = schemaList;
      this.schemaFilter = schemaFilter;
      this.schema.find(( item ) => item.field === 'playerCode').td.data = idPathMap;
    });

    hubEntityService.entitySelected$
      .pipe(
        filter(data => !!data),
        takeUntil(this.destroy$)
      )
      .subscribe(entity => {
        this.path = entity.path;
        this.isRoot = entity.path === ':';
      });
  }

  ngOnInit() {
    this.entityDataSourceService.show();

    this.hubEntityService.entitySelected$
      .pipe(
        skip(1),
        takeUntil(this.destroy$)
      )
      .subscribe(() => {
        this.filterDataService.patchFilter({ gameCode: null });
      });

    this.hubEntityService.entitySelected$.pipe(
      filter(entity => !!entity),
      switchMap(( entity: SwHubShortEntity ) => this.gameService.getAllGames(entity.path, false, true)),
      map(( games: Game[] ) =>
        games.map<SwuiSelectOption>(( { code, title } ) => ({ id: code, text: `${title} (${code})` }))),
      takeUntil(this.destroy$),
    ).subscribe(( games: SwuiSelectOption[] ) => {
      this.schemaFilter = this.schemaFilter.map(( item ) => {
        if (item.field === 'gameCode' && item.type === 'select') {
          item.data = games || [];
        }
        return item;
      });
    });

    this.actions.push(
      new RowAction({
        icon: 'history',
        inMenu: false,
        title: 'View details',
        fn: ( roundInfo ) => {
          const { roundId, extTrxId, gameProviderCode } = roundInfo;
          this.hubEntityService.entitySelected$
            .pipe(
              take(1),
              switchMap(entity => {
                const path = entity.path.charAt(0) === ':' ? entity.path.substring(1) : entity.path;
                return this.gameHistoryGameHistoryService.getExternalGameHistoryDetails(roundId, extTrxId, gameProviderCode, path);
              })
            )
            .subscribe(details => {
              this.attachDetails(roundInfo, details);
            });
        },
      })
    );

    this.actions.push(
      new RowAction({
        icon: 'flash_on',
        inMenu: false,
        title: 'Force finish',
        availableFn: ( roundInfo ) => {
          const { gameProviderCode, finished } = roundInfo;
          const allowed = this.isRoot
            ? this.authService.allowedTo([PERMISSIONS_NAMES.KEYENTITY_EXTERNAL_GAME_PROVIDER_GAMECLOSE_FORCEFINISH])
            : this.authService.allowedTo([PERMISSIONS_NAMES.ENTITY_EXTERNAL_GAME_PROVIDER_GAMECLOSE_FORCEFINISH]);

          return gameProviderCode === 'ITG' && allowed && !finished;
        },
        fn: ( roundInfo ) => {
          this.dialog.open(BoConfirmationComponent, {
            width: '500px',
            data: { message: 'GAMEHISTORY.EXTERNAL.messageForceFinish' },
            disableClose: true
          }).afterClosed()
            .pipe(
              take(1),
              filter(res => !!res),
              switchMap(() => this.service.forceFinish(this.path, roundInfo))
            )
            .subscribe(() => {
              this.grid.dataSource.loadData();
              this.notifications.success(this.translate.instant('GAMEHISTORY.INTERNAL.NOTIFICATIONS.roundFinished'));
            });
        },
      })
    );

    this.actions.push(
      new RowAction({
        icon: 'check_box',
        inMenu: false,
        title: 'GAMEHISTORY.INTERNAL.finalize',
        availableFn: ( { gameProviderCode, finished } ) => {
          if (finished || gameProviderCode !== 'ITG') {
            return false;
          }
          const permission = this.isRoot ? PERMISSIONS_NAMES.KEYENTITY_GAMECLOSE_FINALIZE : PERMISSIONS_NAMES.ENTITY_GAMECLOSE_FINALIZE;
          return this.authService.allowedTo([permission]);
        },
        fn: ( roundInfo ) => {
          this.dialog.open(BoConfirmationComponent, {
            width: '500px',
            data: { message: 'GAMEHISTORY.INTERNAL.messageFinalize' },
            disableClose: true
          }).afterClosed()
            .pipe(
              take(1),
              filter(res => !!res),
              switchMap(() => this.service.finalize(this.path, roundInfo))
            )
            .subscribe(() => {
              this.grid.dataSource.loadData();
              this.notifications.success(this.translate.instant('GAMEHISTORY.INTERNAL.NOTIFICATIONS.roundFinished'));
            });
        },
      })
    );
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
    this.entityDataSourceService.hide();
  }

  downloadCsv() {
    this.loading = true;
    this.service.downloadCsv()
      .pipe(
        catchError(( err ) => {
          this.loading = false;
          return throwError(err);
        }),
        takeUntil(this.destroy$)
      ).subscribe(() => this.loading = false);
  }

  exportPage() {
    this.service.exportPage(this.grid.dataSource.data, this.grid.displayedColumns, this.grid.paginator.pageIndex + 1);
  }

  private attachDetails( roundInfo, details ) {
    let parsedDetails;
    let url = null;

    const detailsResult = details.result;

    if (detailsResult.indexOf('data:image') === 0) {
      parsedDetails = this.sanitizer.bypassSecurityTrustHtml(
        `<div class="text-center"><img alt src="${detailsResult}"></div>`
      );
    } else {
      parsedDetails = this.sanitizer.bypassSecurityTrustHtml(detailsResult);
    }

    if (details.type === 'url') {
      url = details.result;
    }

    this.dialog.open(ExternalRoundInfoViewModalComponent, {
      data: {
        roundInfo,
        details: parsedDetails,
        url: url
      },
      width: '1000px',
      disableClose: true
    });
  }
}
