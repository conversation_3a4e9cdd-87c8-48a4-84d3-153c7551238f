import { Injectable } from '@angular/core';
import { ReplaySubject, throwError } from 'rxjs';
import { PERMISSIONS_LIST, PERMISSIONS_NAMES } from '../../app.constants';
import { SwHubAuthService } from '@skywind-group/lib-swui';
import { Tab } from './components/entities/setup-entity.component';
import { Entity } from '../../common/typings';
import { EntityService } from '../../common/services/entity.service';
import { catchError, take } from 'rxjs/operators';

@Injectable()
export class EntityStateService {
  tabs: Tab[] = [
    {name: 'regional', displayName: 'ENTITY_SETUP.REGIONAL.tabname'},
    {name: 'users', displayName: 'ENTITY_SETUP.USERS.tabname'},
    {name: 'notifications', displayName: 'ENTITY_SETUP.NOTIFICATIONS.tabname'},
    {name: 'games', displayName: 'ENTITY_SETUP.GAMES.tabname'},
    // { name: 'whitelist', displayName:  'ENTITY_SETUP.WHITELISTING.tabname' },
    {name: 'players', displayName: 'ENTITY_SETUP.PLAYER_BLOCKLIST.tabname'},
    {name: '2fa', displayName: 'ENTITY_SETUP.TWOFA_SETUP.tabname'},
    {name: 'email-template', displayName: 'ENTITY_SETUP.EMAIL_TEMPLATE.tabName'},
    {name: 'domains', displayName: 'ENTITY_SETUP.DOMAINS.tabname'},
    {name: 'additional', displayName: 'ENTITY_SETUP.ADDITIONAL.additional'},
    {name: 'game-limits', displayName: 'ENTITY_SETUP.GAME_LIMITS.tabName'},
    {name: 'game-group', displayName: 'ENTITY_SETUP.GAME_GROUP.tabName'},
    {name: 'engagement', displayName: 'ENTITY_SETUP.ENGAGEMENT.tabName'},
    {name: 'rtp-reducer', displayName: 'ENTITY_SETUP.RTP_REDUCER.tabName'},
    {name: 'game-group-filters', displayName: 'ENTITY_SETUP.GAME_GROUP_FILTERS.tabName'},
    {name: 'test-players', displayName: 'ENTITY_SETUP.TEST_PLAYERS.tabName'},
  ];
  tabs$ = new ReplaySubject<Tab[]>(1);

  constructor(private authService: SwHubAuthService,
              private service: EntityService<Entity>) {
  }

  initEntity(path: string) {
    const request = path === ':' || !path ? this.service.getBrief() : this.service.getItem(path);

    request
      .pipe(
        take(1),
        catchError(err => {
          this.resetEntity();
          return throwError(err);
        })
      )
      .subscribe((entity: Entity) => {
        entity.path = path;
        this.initTabsAccess(entity);
      });
  }

  resetEntity() {
    this.tabs$.next([]);
  }

  private initTabsAccess(entity: Entity) {
    const isRoot = entity.path === ':' || !entity.path;
    const notRoot = !isRoot;
    const isMaster = isRoot && entity.name === 'MASTER';
    const isReseller = entity.type === 'entity';

    const allowedUsers = (notRoot && this.authService.allowedTo(PERMISSIONS_LIST.USER_VIEW))
      || (this.keyentityUserExtraAllowed() && !notRoot && !isMaster);

    const accessList = {
      'regional': notRoot,
      'users': allowedUsers,
      'games': notRoot && this.authService.allowedTo([PERMISSIONS_NAMES.GAME_GROUP_VIEW, ...PERMISSIONS_LIST.ENTITY_GAME_VIEW]),
      'whitelist': notRoot,
      'notifications': this.authService.allowedTo(PERMISSIONS_LIST.NOTIFICATIONS),
      'players': notRoot && !isReseller && this.authService.allowedTo(['player:change-state']),
      '2fa': !isMaster && this.authService.allowedTo([PERMISSIONS_NAMES.ENTITY_EDIT]),
      'domains': this.authService.allowedTo(PERMISSIONS_LIST.DOMAIN),
      'engagement': this.authService.allowedTo([PERMISSIONS_NAMES.ENTITY_EDIT]),
      'additional': this.authService.allowedTo(PERMISSIONS_LIST.ENTITY),
      'game-limits': this.authService.allowedTo(PERMISSIONS_LIST.GAME_GROUP_CREATE),
      'game-group': this.authService.allowedTo(PERMISSIONS_LIST.GAME_GROUP_VIEW),
      'rtp-reducer': this.authService.allowedTo(PERMISSIONS_LIST.GAMERTP),
      'game-group-filters': this.authService.allowedTo(PERMISSIONS_LIST.GAME_GROUP_VIEW),
      'email-template': (this.authService.allowedTo(PERMISSIONS_LIST.ENTITY) && this.authService.isSuperAdmin),
      'test-players': this.authService.isSuperAdmin && notRoot && !isReseller,
    };

    this.tabs.forEach((tab) => {
      if (tab.name in accessList) {
        tab.available = accessList[tab.name];
      }
    });
    this.tabs$.next(this.tabs);
  }

  private keyentityUserExtraAllowed(): boolean {
    const forceEmail: boolean = this.authService.allowedTo([PERMISSIONS_NAMES.KEYENTITY_FORCE_SET_EMAIL]);
    const forcePwd: boolean = this.authService.allowedTo([PERMISSIONS_NAMES.KEYENTITY_FORCE_RESET_PASSWORD]);
    const userDelete: boolean = this.authService.allowedTo([PERMISSIONS_NAMES.KEYENTITY_USER_DELETE]);

    return forceEmail || forcePwd || userDelete;
  }
}
