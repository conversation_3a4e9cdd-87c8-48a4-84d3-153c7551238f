import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { PERMISSIONS_LIST } from '../../../../app.constants';
import { BriefResolver } from '../../../../common/services/resolvers/brief.resolver';
import { StructureResolver } from '../../../../common/services/resolvers/structure.resolver';
import { MoveEntityComponent } from './move-entity.component';


const routes = [
  {
    path: 'move',
    component: MoveEntityComponent,
    data: {
      title: 'Move Entity',
      permissions: PERMISSIONS_LIST.ENTITY,
    },
    resolve: {
      entity: BriefResolver,
      structure: StructureResolver,
    }
  },
  {
    path: 'move/:entityKey',
    component: MoveEntityComponent,
    data: {
      title: 'Move Entity',
      permissions: PERMISSIONS_LIST.ENTITY,
    },
    resolve: {
      entity: BriefResolver,
      structure: StructureResolver,
    }
  }
];

@NgModule({
  imports: [
    RouterModule.forChild(routes)
  ],
  exports: [
    RouterModule
  ],
})
export class MoveEntityRoutingModule {
}
