<lib-swui-page-panel [title]="'BUSINESS_STRUCTURE.MOVE_ENTITY.title'"
                     backUrl="/pages/business-management/business-structure">
</lib-swui-page-panel>

<mat-card class="mat-elevation-z0 m-32">
  <div class="body">
    <h6>{{ 'BUSINESS_STRUCTURE.MOVE_ENTITY.description' | translate }}</h6>

    <form [formGroup]="form" fxLayout="row">
      <mat-form-field appearance="outline" class="select">
        <lib-swui-select
          [placeholder]="'BUSINESS_STRUCTURE.MOVE_ENTITY.entityBrandMerchantToMovePlaceholder' | translate"
          [matTooltip]="entityPath"
          [data]="structureData"
          [showSearch]="true"
          [disableEmptyOption]="true"
          [formControl]="entityPathControl">
        </lib-swui-select>
        <mat-error>
          <lib-swui-control-messages
            [messages]="messageErrors"
            [control]="entityPathControl">
          </lib-swui-control-messages>
        </mat-error>
      </mat-form-field>

      <mat-form-field appearance="outline" class="select ml-20">
        <lib-swui-select
          [placeholder]="'BUSINESS_STRUCTURE.MOVE_ENTITY.newParentEntityPlaceholder' | translate"
          [matTooltip]="newEntityParentPath"
          [data]="entitiesList"
          [showSearch]="true"
          [disableEmptyOption]="true"
          [formControl]="newParentPathControl">
        </lib-swui-select>
        <mat-error>
          <lib-swui-control-messages
            [messages]="messageErrors"
            [control]="newParentPathControl">
          </lib-swui-control-messages>
        </mat-error>
      </mat-form-field>
    </form>

    <button mat-flat-button color="primary"
            class="mat-button-md"
            [disabled]="entityPathControl.value === newParentPathControl.value"
            (click)="move()">
      {{ 'BUSINESS_STRUCTURE.MOVE_ENTITY.move' | translate }}
    </button>
  </div>
</mat-card>

<mat-card *ngIf="error" class="mat-elevation-z0 m-32">
  <div class="body">
    <span class="mat-error">
      {{ ('BUSINESS_STRUCTURE.MOVE_ENTITY.errorCode' | translate) + ': ' + error.code + ' - ' + error.message  }}
    </span>

    <div *ngIf="error.conflicts">
      <div class="mat-table">
        <div class="mat-row" *ngFor="let conflict of error.conflicts | keys">
          <div class="mat-cell">
            <span class="mat-error">
              {{ conflict }}
            </span>
          </div>
          <div class="mat-cell" [ngSwitch]="isArray(error.conflicts[conflict])">
            <ng-template ngSwitchCase="true">
              <code *ngFor="let item of error.conflicts[conflict]; last as isLast">
                {{ item }} {{ isLast ? '' : ', ' }}
              </code>
            </ng-template>
            <code *ngSwitchDefault>
              {{ error.conflicts[conflict] }}
            </code>
          </div>
        </div>
      </div>
    </div>
  </div>
</mat-card>
