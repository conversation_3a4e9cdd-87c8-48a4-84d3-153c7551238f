import { HttpErrorResponse } from '@angular/common/http';
import { Component, OnDestroy } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { SwuiNotificationsService, SwuiSelectOption } from '@skywind-group/lib-swui';
import { Subject } from 'rxjs';
import { finalize, takeUntil } from 'rxjs/operators';
import { ErrorMessage } from '../../../../common/components/mat-user-editor/user-form.component';
import { Entity } from '../../../../common/models/entity.model';
import { SelectOptionModel } from '../../../../common/models/select-option.model';
import { entitiesStructureToSelectOptions, EntityService } from '../../../../common/services/entity.service';
import { StructureResolver } from '../../../../common/services/resolvers/structure.resolver';

@Component({
  selector: 'move-entity',
  templateUrl: 'move-entity.component.html',
  styleUrls: ['./move-entity.component.scss'],
})

export class MoveEntityComponent implements OnDestroy {
  messageErrors: ErrorMessage = {
    required: 'VALIDATION.required',
  };

  get entityPath(): string {
    return this._entityPath;
  }

  get newEntityParentPath(): string {
    return this._newEntityParentPath;
  }

  public form: FormGroup;
  public structureData: SwuiSelectOption[] = [];
  public entitiesList: SwuiSelectOption[] = [];

  public error: any = undefined;

  private _newEntityParentPath: string;
  private _entityPath: string;
  private destroyed$ = new Subject<void>();

  constructor( private service: EntityService<Entity>,
               private fb: FormBuilder,
               private route: ActivatedRoute,
               private structureResolver: StructureResolver,
               private notifications: SwuiNotificationsService,
               private translate: TranslateService,
  ) {
    const { structure } = this.route.snapshot.data;
    this.processStructure(structure);

    const { entityKey } = route.snapshot.params;
    const selectedEntity = this.structureData.find(i => i.data.key === entityKey);

    this.form = this.fb.group({
      entityPath: [selectedEntity.id, Validators.required],
      newParentPath: ['', Validators.required],
    });

    this.form.valueChanges
      .pipe(
        takeUntil(this.destroyed$),
      ).subscribe(() => this.error = undefined);

    this._entityPath = this.getSelectedEntityName(selectedEntity.id);
  }

  ngOnInit() {
    this.entityPathControl.valueChanges.pipe(
      takeUntil(this.destroyed$)
    ).subscribe(( selectedEntityPath: string ) => {
      this._entityPath = this.getSelectedEntityName(selectedEntityPath);
    });

    this.newParentPathControl.valueChanges.pipe(
      takeUntil(this.destroyed$)
    ).subscribe(( selectedEntityPath: string ) => {
      this._newEntityParentPath = this.getSelectedEntityName(selectedEntityPath);
    });
  }

  ngOnDestroy() {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  get entityPathControl(): FormControl {
    return this.form.get('entityPath') as FormControl;
  }

  get newParentPathControl(): FormControl {
    return this.form.get('newParentPath') as FormControl;
  }

  getSelectedEntityName( entitySelected: string ): string {
    return this.structureData?.find(entity => entity.id === entitySelected || '')?.text;
  }

  public move() {
    this.form.markAllAsTouched();

    if (this.form.valid) {
      this.error = undefined;

      const { entityPath, newParentPath } = this.form.value;
      const entityKey = this.structureData.find(( i ) => i.id === entityPath);
      const newParentKey = this.structureData.find(( i ) => i.id === newParentPath);

      this.service.moveEntity(entityKey.data.key, newParentKey.data.key)
        .pipe(
          takeUntil(this.destroyed$),
          finalize(
            () => {
              this.updateStructure();
            }
          ),
        )
        .subscribe(
          () => {
            this.notifications.success(
              this.translate.instant(
                'BUSINESS_STRUCTURE.MOVE_ENTITY.successNotification',
                {
                  entityPath: this.entityPathControl.value,
                  newParentPath: this.newParentPathControl.value,
                }
              )
            );

            this.form.reset();
          },
          ( error: HttpErrorResponse ) => {
            this.error = error?.error;

            this.notifications.error(error?.error?.message);
          },
        );
    }
  }

  public isArray( item ) {
    return Array.isArray(item);
  }

  private updateStructure() {
    this.structureResolver.resolve()
      .pipe(
        takeUntil(this.destroyed$),
      )
      .subscribe(structure => this.processStructure(structure));
  }

  private processStructure( structure ) {
    this.structureData = entitiesStructureToSelectOptions(structure, 0, [], false)?.map(
      ( item: SelectOptionModel ) => ({ id: item.id, text: item.text, data: item.data }));
    this.entitiesList = this.structureData?.filter(( item: SwuiSelectOption ) => item.data?.type === 'entity');
  }
}
