.search-popup {
  position: fixed;
  left: 50%;
  top: 10px;
  transform: translateX(-50%);
  width: 350px;
  display: none;

  &--visible {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

.search-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.search-buttons {
  display: flex;
  align-items: center;
  border-left: 1px solid rgba(0, 0, 0, .3);
  margin-left: 5px;

  ::ng-deep button.mat-icon-button {
    width: 22px;
    height: 22px;
    line-height: 22px;
  }
}

.search-counter-disabled {
  opacity: .3;
}

.search-input {
  width: 200px;
}

.close-icon {
  cursor: pointer;
}
