<div
  class="search-popup"
  [class.search-popup--visible]="searchVisible"
>
  <mat-card>
    <div class="search-card">
      <mat-form-field class="search-input">
        <input
          matInput
          #inputElement
          type="text"
          [formControl]="inputControl">
        <mat-hint *ngIf="searchVisible && this.inputControl.value">
          <ng-content></ng-content>
        </mat-hint>
      </mat-form-field>
      <span [class.search-counter-disabled]="!length">{{index}}/{{length}}</span>
      <div class="search-buttons">
        <button mat-icon-button [disabled]="!length">
          <mat-icon (click)="prev.emit()">keyboard_arrow_up</mat-icon>
        </button>
        <button mat-icon-button [disabled]="!length">
          <mat-icon (click)="next.emit()">keyboard_arrow_down</mat-icon>
        </button>
        <button mat-icon-button>
          <mat-icon class="close-icon" (click)="closePopup()">close</mat-icon>
        </button>
      </div>
    </div>
    <div *ngIf="isCheckboxVisible">
      <mat-checkbox [formControl]="checkboxControl">Search by decoded id only</mat-checkbox>
    </div>
  </mat-card>
</div>
