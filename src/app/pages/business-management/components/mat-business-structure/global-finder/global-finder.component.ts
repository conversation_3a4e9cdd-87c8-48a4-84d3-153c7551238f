import {
  AfterContentInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, EventEmitter, Input, isDevMode, OnDestroy, OnInit,
  Output, ViewChild
} from '@angular/core';
import { FormControl } from '@angular/forms';
import { PERMISSIONS_NAMES, SwHubAuthService } from '@skywind-group/lib-swui';
import { combineLatest, fromEvent, ReplaySubject, Subject, Subscription } from 'rxjs';
import { debounceTime, filter, startWith, take, takeUntil } from 'rxjs/operators';

export interface GlobalFinderState {
  text: string;
  id?: string;
  position?: number;
  checked?: boolean;
}

@Component({
  selector: 'global-finder',
  templateUrl: './global-finder.component.html',
  styleUrls: ['./global-finder.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class GlobalFinderComponent implements OnInit, AfterContentInit, On<PERSON><PERSON>roy {

  @Input() index: number = 0;
  @Input() length: number = 0;

  @Input() set state( state: GlobalFinderState ) {
    if (!state) {
      return;
    }
    if (this._contentInitialized) {
      if (isDevMode()) {
        throw new Error('Cannot change state mode after initialization.');
      }
    } else {
      this.state$.next(state);
    }
  }

  @Output() valueChange = new EventEmitter<{ value: string, checked: boolean }>();
  @Output() next = new EventEmitter<string>();
  @Output() prev = new EventEmitter<string>();
  @Output() close = new EventEmitter<void>();
  @Output() stateChange = new EventEmitter<GlobalFinderState>();

  searchVisible = false;
  findSubscription = new Subscription();
  inputControl = new FormControl('');
  checkboxControl = new FormControl(false);
  readonly isCheckboxVisible: boolean;

  @ViewChild('inputElement', { read: ElementRef }) inputElement: ElementRef;

  private state$ = new ReplaySubject<GlobalFinderState>(1);
  private _contentInitialized = false;
  private destroy$ = new Subject();

  constructor( private cdr: ChangeDetectorRef,
               auth: SwHubAuthService ) {
    this.isCheckboxVisible = auth.allowedTo([PERMISSIONS_NAMES.ID_DECODE]);
  }

  ngOnInit() {

    fromEvent(window, 'keydown')
      .pipe(
        filter(( e: KeyboardEvent ) => (e.metaKey || e.ctrlKey) && e.code === 'KeyF'),
        takeUntil(this.destroy$),
      )
      .subscribe(( e: KeyboardEvent ) => {
        e.preventDefault();

        this.showPopup();
      });

    combineLatest([this.inputControl.valueChanges, this.checkboxControl.valueChanges.pipe(startWith(false))])
      .pipe(
        debounceTime(250),
        takeUntil(this.destroy$),
      )
      .subscribe(( [value, checked] ) => {
        this.valueChange.emit({ value, checked });
      });

    this.state$
      .pipe(take(1))
      .subscribe(state => {
        this.showPopup();
        this.inputControl.setValue(state.text, { emitEvent: false });
        this.stateChange.emit(state);
      });
  }

  ngAfterContentInit() {
    this._contentInitialized = true;
  }

  showPopup() {
    this.searchVisible = true;
    this.cdr.detectChanges();
    this.inputElement.nativeElement.focus();

    this.findSubscription = fromEvent(this.inputElement.nativeElement, 'keypress')
      .pipe(
        filter(( { code } ) => code === 'Enter'),
      )
      .subscribe(() => {
        this.next.emit(this.inputControl.value);
      });
  }

  closePopup() {
    this.searchVisible = false;
    this.inputControl.setValue('', { emitEvent: false });
    this.close.emit();
    this.findSubscription.unsubscribe();
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

}
