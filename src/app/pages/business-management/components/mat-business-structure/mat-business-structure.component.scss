.entities-wrapper {
  height: calc(100vh - 122px);
}

.bs-table {
  width: 100%;
  border-collapse: separate;
  border-color: grey;

  thead {
    vertical-align: middle;
    font-weight: 400;
    tr {
      height: 48px;
      th {
        border-bottom-color: rgba(0, 0, 0, .12);
        border-bottom-width: 1px;
        border-bottom-style: solid;
        padding: 0 8px;
        font-size: 14px;
        font-weight: 500;
        color: rgba(0, 0, 0, .54);
        text-align: center;
        &:first-of-type {
          padding-left: 15px;
        }
      }
    }
  }

  &-body {
    display: table-row-group;
  }

  &-row {
    display: table-row;
    transition: background-color 0.15s ease-in-out;

    &--selected {
      background-color: rgba(255, 255, 0, .3);
    }

    &:hover {
      background-color: rgba(0, 0, 0, .03);
    }
  }
}

.bs-loading {
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
  background: rgba(0, 0, 0, .12);
}

.bs-structure-wrapper {
  background: white;
  width: 100%;
  overflow: auto;
  margin-bottom: 24px;
}

.toggle-expand-button {
  padding: 0;
  margin: 0;
  visibility: hidden;
}

.expand-visible {
  visibility: visible;
}
