import { ScrollingModule } from '@angular/cdk/scrolling';
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FlexLayoutModule } from '@angular/flex-layout';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatChipsModule } from '@angular/material/chips';
import { MatDialogModule } from '@angular/material/dialog';
import { MatDividerModule } from '@angular/material/divider';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTableModule } from '@angular/material/table';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatTreeModule } from '@angular/material/tree';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiGridRowActionsModule, SwuiPagePanelModule } from '@skywind-group/lib-swui';
import { TrimInputValueModule } from '../../../../common/directives/trim-input-value/trim-input-value.module';
import { PipesModule } from '../../../../common/pipes/pipes.module';
import { EntityLabelsService } from '../../../../common/services/entity-labels.service';
import { EntitySettingsService } from '../../../../common/services/entity-settings.service';
import { GameService } from '../../../../common/services/game.service';
import { MerchantTypesService } from '../../../../common/services/merchant-types.service';
import { BusinessStructureService } from './business-structure.service';
import { EntityLabelsDialogModule } from './dialogs/entity-labels-dialog/entity-labels-dialog.module';
import { ManageBalanceModule } from './dialogs/manage-balance/manage-balance.module';
import { MatCountryDialogModule } from './dialogs/mat-country-dialog/mat-country-dialog.module';
import { MatEntityEditDialogModule } from './dialogs/mat-entity-edit-dialog/mat-entity-edit-dialog.module';
import { MatLanguageDialogModule } from './dialogs/mat-language-dialog/mat-language-dialog.module';
import { MatRegionalDialogModule } from './dialogs/mat-regional-dialog/mat-regional-dialog.module';
import { SetupHintDialogModule } from './dialogs/setup-hint-dialog/setup-hint-dialog.module';
import { ShowLimitsModalComponent } from './dialogs/show-limits-modal/show-limits-modal.component';
import { ShowLimitsModalModule } from './dialogs/show-limits-modal/show-limits-modal.module';
import { StatusConfirmModule } from './dialogs/status-confirm/status-confirm.module';
import { EntityItemComponent } from './entity-item/entity-item.component';
import { GlobalFinderComponent } from './global-finder/global-finder.component';
import { MatBusinessStructureComponent } from './mat-business-structure.component';
import { SearchByDomainModalComponent } from './search-by-domain-modal/search-by-domain-modal.component';
import { SearchByDomainModalModule } from './search-by-domain-modal/search-by-domain-modal.module';

export const bsMatModules = [
  MatDividerModule,
  MatTreeModule,
  MatButtonModule,
  MatIconModule,
  MatProgressBarModule,
  MatProgressSpinnerModule,
  MatTableModule,
  MatMenuModule,
  MatChipsModule,
  MatDialogModule,
  SwuiPagePanelModule,
  MatTooltipModule,
  PipesModule,
];

@NgModule({
  imports: [
    CommonModule,
    FlexLayoutModule,
    TranslateModule,
    RouterModule,
    ...bsMatModules,
    ScrollingModule,
    ManageBalanceModule,
    MatCountryDialogModule,
    StatusConfirmModule,
    MatLanguageDialogModule,
    MatRegionalDialogModule,
    MatEntityEditDialogModule,
    SearchByDomainModalModule,
    ShowLimitsModalModule,
    ReactiveFormsModule,
    MatInputModule,
    MatCardModule,
    EntityLabelsDialogModule,
    SetupHintDialogModule,
    SwuiGridRowActionsModule,
    TrimInputValueModule,
    MatCheckboxModule,
  ],
  declarations: [
    MatBusinessStructureComponent,
    EntityItemComponent,
    GlobalFinderComponent
  ],
  exports: [
    MatBusinessStructureComponent,
  ],
  providers: [
    GameService,
    BusinessStructureService,
    EntitySettingsService,
    MerchantTypesService,
    EntityLabelsService,
  ],
  entryComponents: [
    SearchByDomainModalComponent,
    ShowLimitsModalComponent,
  ],
})
export class MatBusinessStructureModule {
}
