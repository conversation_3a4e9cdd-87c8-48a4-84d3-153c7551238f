<lib-swui-page-panel
  [title]="'MENU_SECTIONS.businessManagement_structure'"
  [actions]="panelActions">
</lib-swui-page-panel>

<cdk-virtual-scroll-viewport
  class="entities-wrapper"
  [minBufferPx]="3960"
  [maxBufferPx]="4008"
  [itemSize]="48">
  <div *ngIf="!loading" class="p-32">
    <div class="bs-structure-wrapper">

      <table class="bs-table">

        <thead>
        <tr>
          <th style="text-align: left">{{'BUSINESS_STRUCTURE.title' | translate}}</th>
          <th style="text-align: left">{{'BUSINESS_STRUCTURE.code' | translate}}</th>
          <th style="text-align: left">{{'BUSINESS_STRUCTURE.key' | translate}}</th>
          <th style="text-align: center">{{'BUSINESS_STRUCTURE.regional' | translate}}</th>
          <th style="text-align: center">{{'BUSINESS_STRUCTURE.status' | translate}}</th>
          <th style="text-align: center" *ngIf="!isBrand">{{'BUSINESS_STRUCTURE.actions' | translate}}</th>
        </tr>
        </thead>

        <tr
          entity-item
          class="bs-table-row"
          [class.bs-table-row--selected]="entity.id === foundIds[foundIndex]"
          *cdkVirtualFor="let entity of entities; let i = index; trackBy trackByFn"
          [entity]="entity"
          [disabledCurrencyTooltips]="disabledCurrencyTooltips"
          [canChangeTest]="canChangeTest"
          [expanded]="bsService.expandedEntities.get(entity.id)"
          (onClick)="handleExpandCollapse($event)"
        ></tr>

      </table>

    </div>
  </div>

</cdk-virtual-scroll-viewport>

<div class="bs-loading" *ngIf="loading || entityLoading">
  <mat-progress-spinner diameter="48" mode="indeterminate"></mat-progress-spinner>
</div>

<global-finder
  *ngIf="this.viewPort?.getDataLength() > 0"
  [index]="foundIndex + 1"
  [length]="foundIds?.length"
  [state]="searchState"
  (prev)="onFind(-1)"
  (next)="onFind(1)"
  (valueChange)="onFindChange($event)"
  (stateChange)="onStateFindChange($event)"
  (close)="onFindClose()"
>
  <ng-container *ngIf="!foundIds?.length">Nothing is found</ng-container>
</global-finder>
