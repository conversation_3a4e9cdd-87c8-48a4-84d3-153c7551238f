import { Injectable } from '@angular/core';
import { GlobalFinderState } from './global-finder/global-finder.component';

@Injectable()
export class MatBusinessStructureSearchService {
  private _searchState: GlobalFinderState | null = null;

  get searchState(): GlobalFinderState | null {
    return this._searchState;
  }

  setText(text: string) {
    this._searchState = {...this._searchState, text};
  }

  setFoundedEntityId(id: string) {
    this._searchState = {...this._searchState, id};
  }

  setPosition(position: number) {
    this._searchState = {...this._searchState, position};
  }

  clearState() {
    this._searchState = null;
  }
}
