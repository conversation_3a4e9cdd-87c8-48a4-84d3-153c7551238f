import { NgModule } from '@angular/core';
import { MatCardModule } from '@angular/material/card';
import { TranslateModule } from '@ngx-translate/core';
import { ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { TrimInputValueModule } from '../../../../../common/directives/trim-input-value/trim-input-value.module';
import { SearchByDomainModalComponent } from './search-by-domain-modal.component';
import { ControlMessagesModule } from '../../../../../common/components/control-messages/control-messages.module';
import { MatDialogModule } from '@angular/material/dialog';
import { MatInputModule } from '@angular/material/input';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatButtonModule } from '@angular/material/button';


@NgModule({
    imports: [
        CommonModule,
        TranslateModule,
        MatDialogModule,
        ReactiveFormsModule,
        MatInputModule,
        MatButtonModule,
        ControlMessagesModule,
        RouterModule,
        MatProgressSpinnerModule,
        MatCardModule,
        TrimInputValueModule,
    ],
  exports: [],
  declarations: [SearchByDomainModalComponent],
  providers: [],
})
export class SearchByDomainModalModule {
}
