<form [formGroup]="searchForm">
    <mat-form-field appearance="outline" class="width100">
      <mat-label>{{ 'ENTITY_SETUP.WHITELISTING.MODALS.siteUrl' | translate }}</mat-label>
      <input matInput trimValue
             type="text"
             autocomplete="off"
             [formControl]="siteUrlControl">
    </mat-form-field>
    <control-messages
      class="validation-error-label"
      [control]="siteUrlControl">
    </control-messages>
</form>

<mat-card>
  <mat-card-title>
    <h6>
      {{ 'ENTITY_SETUP.WHITELISTING.MODALS.siteUrl' | translate }} : {{ siteUrl }}
    </h6>
  </mat-card-title>

  <mat-card-content class="modal-scroll">
    <ng-container *ngIf="loading; else loadingBlock">
      <ng-container *ngIf="entities.length > 0 && siteUrl != ''; else noMatches">
        <ul class="media-list media-list-bordered">
          <li class="media" *ngFor="let entity of entities">
            <ng-container *ngIf="!entity.disabled">
              <h6>{{ 'ENTITY_SETUP.REGIONAL.MODALS.entityPath' | translate }}:
                {{ entity.visiblePath }}
              </h6>
              <h6>{{ 'ENTITY_SETUP.REGIONAL.MODALS.path' | translate }}:
                <a (click)="navigateTo2FaPage(entity)">
                  {{ entity.text }}
                </a>
              </h6>
            </ng-container>
          </li>
        </ul>
      </ng-container>
    </ng-container>

    <ng-template #loadingBlock>
      <mat-spinner diameter="24">
        {{ 'BUSINESS_STRUCTURE.WIDGETS.loading' | translate }}
      </mat-spinner>
    </ng-template>
    <ng-template #noMatches>
      <h4>
        {{ 'COMPONENTS.GRID.EMPTY_LIST' | translate }}
      </h4>
    </ng-template>
  </mat-card-content>
</mat-card>

<mat-dialog-actions align="end">
  <button mat-button mat-dialog-close>
    {{ 'DIALOG.close' | translate }}
  </button>
</mat-dialog-actions>
