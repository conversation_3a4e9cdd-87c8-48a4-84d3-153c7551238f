import { Component, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormControl, FormGroup, Validators } from '@angular/forms';
import { MatDialogRef } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { Subject } from 'rxjs';
import { debounceTime, distinctUntilChanged, filter, map, switchMap, takeUntil, tap } from 'rxjs/operators';

import { Entity } from '../../../../../common/models/entity.model';
import { SelectOptionModel } from '../../../../../common/models/select-option.model';
import { EntityService } from '../../../../../common/services/entity.service';
import { ValidationService } from '../../../../../common/services/validation.service';
import { EntityShortInterface } from '../../../../../common/typings';
import { ENTITY_SITES_AVAILABLE_ANCHOR } from '../../entities/tab-2fa/tab-2fa.component';


@Component({
  selector: 'search-by-domain-modal',
  templateUrl: 'search-by-domain-modal.component.html',
  styleUrls: ['search-by-domain-modal.component.scss']
})
export class SearchByDomainModalComponent implements OnInit {
  siteUrl: string;
  entities: SelectOptionModel[] = [];
  searchForm: FormGroup;
  loading = true;

  private destroyed$ = new Subject<void>();

  get siteUrlControl(): FormControl {
    return this.searchForm.get('siteUrl') as FormControl;
  }

  constructor(public  dialogRef: MatDialogRef<SearchByDomainModalComponent>,
              private fb: FormBuilder,
              private entityService: EntityService<Entity>,
              private router: Router,
              private activatedRoute: ActivatedRoute,
  ) {
    this.searchForm = this.fb.group({
      siteUrl: ['', Validators.compose([
        ValidationService.maxLength(100)
      ])],
    });
  }

  ngOnInit(){
    this.siteUrlControl.valueChanges.pipe(
      debounceTime(250),
      distinctUntilChanged(),
      tap(() => this.siteUrl = ''),
      filter(( url: string ) => url && url !== '' ),
      tap((url: string ) =>  {
        this.loading = false;
        this.siteUrl = url;
      }),
      switchMap(( siteUrl: string ) => this.entityService.searchEntityInShortStructure({ url__contains: siteUrl })),
      map(entity => entity === null ? undefined : entity ),
      map((structure) => this.entitiesStructure(structure, 0, [], true)),
      map((entities: SelectOptionModel[]) => entities.filter(entity => entity.disabled === false )),
      tap((entities: SelectOptionModel[]) => this.entities = entities ),
      takeUntil(this.destroyed$)
    ).subscribe(() => this.loading = true );
  }

  navigateTo2FaPage(entity: SelectOptionModel) {
    this.activatedRoute.snapshot.fragment = '2fa';
    this.router.navigate(['/pages/business-management/entities/setup/', entity.id, 'p', '2fa'],
      {
        fragment: '2fa',
        queryParams: {
          anchor: ENTITY_SITES_AVAILABLE_ANCHOR
        }
      }).then(() => this.dialogRef.close());
  }

  private entitiesStructure(item: any, count: number = 0, resultArray: SelectOptionModel[] = [],
                            disableEntityEntries = true): SelectOptionModel[] {
    if (item) {
      const {type, key} = item;
      const disabled = disableEntityEntries && ['brand', 'merchant'].indexOf(type) === -1;
      const path = item.path === ':' ? '' : item.path;

      resultArray.push(
        new SelectOptionModel(
          path,
          `${item.name}`,
          disabled,
          {type, key},
          this.getVisiblePath(item))
      );

      if (item.child) item.child.reverse()
        .forEach(i => this.entitiesStructure(i, count + 1, resultArray, disableEntityEntries));
    } else {
      resultArray = [];
    }

    return resultArray;
  }

  private getVisiblePath( item: EntityShortInterface ): string {
    let path = item.path.replace(/:/g, ' > ').trim();
    return path.substring(0, path.length - 1).trim();
  }
}
