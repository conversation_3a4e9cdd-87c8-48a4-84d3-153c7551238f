import { MatTreeFlattener } from '@angular/material/tree';
import { Observable } from 'rxjs';
import { take } from 'rxjs/operators';

export class BsTreeFlattener<T, F> extends MatTreeFlattener<T, F> {

  constructor( public transformFunction: ( node: T, level: number ) => F,
               public getLevel: ( node: F ) => number,
               public isExpandable: ( node: F ) => boolean,
               public getChildren: ( node: T ) =>
                 Observable<T[]> | T[] | undefined | null,
               public tapChild: ( child: F, parent: F ) => void,
  ) {
    super(transformFunction, getLevel, isExpandable, getChildren);
  }

  _flattenNode( node: T, level: number,
                resultNodes: F[], parentMap: boolean[], flatParent?: F ): F[] {
    const flatNode = this.transformFunction(node, level);
    if (flatParent) {
      this.tapChild(flatNode, flatParent);
    }
    resultNodes.push(flatNode);

    if (this.isExpandable(flatNode)) {
      const childrenNodes = this.getChildren(node);
      if (childrenNodes) {
        if (Array.isArray(childrenNodes)) {
          this._flattenChildren(childrenNodes, level, resultNodes, parentMap, flatNode);
        } else {
          childrenNodes.pipe(take(1)).subscribe(children => {
            this._flattenChildren(children, level, resultNodes, parentMap, flatNode);
          });
        }
      }
    }
    return resultNodes;
  }

  _flattenChildren( children: T[], level: number,
                    resultNodes: F[], parentMap: boolean[], flatParent?: F ): void {
    children.forEach(( child, index ) => {
      let childParentMap: boolean[] = parentMap.slice();
      childParentMap.push(index !== children.length - 1);
      this._flattenNode(child, level + 1, resultNodes, childParentMap, flatParent);
    });
  }
}
