import { CdkVirtualScrollViewport } from '@angular/cdk/scrolling';
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  OnDestroy,
  OnInit,
  ViewChild
} from '@angular/core';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { ActivatedRoute } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { PanelAction, PERMISSIONS_NAMES, SwHubAuthService, SwuiNotificationsService } from '@skywind-group/lib-swui';
import { combineLatest, iif, Observable, of, OperatorFunction, Subject, throwError, timer, zip } from 'rxjs';
import { catchError, filter, finalize, map, mapTo, mergeMap, switchMap, take, takeUntil, tap } from 'rxjs/operators';
import { EntitySettingsModel } from '../../../../common/models/entity-settings.model';
import { Entity as EntityModel, Entity } from '../../../../common/models/entity.model';
import { EntitySettingsService } from '../../../../common/services/entity-settings.service';
import { EntityService } from '../../../../common/services/entity.service';
import { GameService } from '../../../../common/services/game.service';
import { MerchantTypeSchema, MerchantTypesService } from '../../../../common/services/merchant-types.service';

import { Country, Currency, Language } from '../../../../common/typings';
import { Jurisdiction } from '../../../../common/typings/jurisdiction';
import { bsActions, bsDialogs, BusinessStructureService, SHORT_STRUCTURE_ADDITIONAL } from './business-structure.service';
import { EditEntitySubmitData, MatEntityEditDialogComponent } from './dialogs/mat-entity-edit-dialog/mat-entity-edit-dialog.component';
import { MatRegionalDialogComponent } from './dialogs/mat-regional-dialog/mat-regional-dialog.component';
import { SetupHintDialogComponent } from './dialogs/setup-hint-dialog/setup-hint-dialog.component';
import { StatusConfirmComponent } from './dialogs/status-confirm/status-confirm.component';
import { GlobalFinderState } from './global-finder/global-finder.component';
import { MatBusinessStructureSearchService } from './mat-business-structure-search.service';
import { SearchByDomainModalComponent } from './search-by-domain-modal/search-by-domain-modal.component';
import { StructureEntityModel } from './structure-entity.model';


@Component({
  selector: 'mat-business-structure',
  templateUrl: './mat-business-structure.component.html',
  styleUrls: ['./mat-business-structure.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class MatBusinessStructureComponent implements OnInit, OnDestroy, AfterViewInit {

  languages: Language[];
  currencies: Currency[];
  countries: Country[];
  jurisdictions: Jurisdiction[];
  loading = true;
  panelActions: PanelAction[] = [];
  merchantTypeSchemas: MerchantTypeSchema[];
  foundIndex = -1;
  foundIds: string[] = [];
  isBrand: boolean;
  viewInit = false;
  searchState: GlobalFinderState;

  entities: Entity[] = [];
  entityLoading = false;
  disabledCurrencyTooltips = false;
  canChangeTest = false;
  @ViewChild(CdkVirtualScrollViewport) viewPort: CdkVirtualScrollViewport;

  private dialogRef: MatDialogRef<any>;
  private destroy$ = new Subject();

  constructor(
    public service: EntityService<StructureEntityModel>,
    public bsService: BusinessStructureService,
    private entitySettingsService: EntitySettingsService<EntitySettingsModel>,
    private merchantTypesService: MerchantTypesService,
    private gameService: GameService,
    private route: ActivatedRoute,
    private cdr: ChangeDetectorRef,
    private dialog: MatDialog,
    private authService: SwHubAuthService,
    private translation: TranslateService,
    private notifications: SwuiNotificationsService,
    private businessStructureSearchService: MatBusinessStructureSearchService
  ) {
    const {brief} = this.route.snapshot.data;
    const briefEntity = new EntityModel(brief);
    this.isBrand = briefEntity.isOperator() || briefEntity.isMerchant;
    this.searchState = businessStructureSearchService.searchState;
    this.disabledCurrencyTooltips = !authService.areGranted([PERMISSIONS_NAMES.ENTITY_BALANCE]);
    this.canChangeTest = authService.areGranted([PERMISSIONS_NAMES.ENTITY_CHANGESTATE_TEST]);
  }

  ngOnInit() {
    this.useRouteResolvedData();
    this.subscribeToStructure();
    this.subscribeToActions();

    if (!this.isBrand) {
      this.setPanelActions();
    }
  }

  ngAfterViewInit() {
    this.viewInit = true;
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  trackByFn(_, item: StructureEntityModel) {
    return item.id;
  }

  isSuperAdmin(): boolean {
    return this.authService.isSuperAdmin;
  }

  subscribeToActions() {
    this.bsService.actions
      .pipe(
        takeUntil(this.destroy$)
      ).subscribe(
      (data) => {
        let {modal, type, entity, actionData} = data;
        if (type === bsActions.SHOW_DIALOG) {
          switch (modal) {

            case bsDialogs.ENTITY_EDIT:
              if (entity.type === Entity.TYPE_MERCHANT) {
                this.onEditItemMerchant(entity);
              } else if (entity.type === Entity.TYPE_BRAND) {
                this.onEditItemBrand(entity);
              } else if (entity.type === Entity.TYPE_ENTITY) {
                this.onEditItemReseller(entity);
              } else {
                this.onEditItem(entity);
              }
              break;

            case bsDialogs.ENTITY_ADD:
              this.onAddItem(entity);
              break;

            case bsDialogs.EDIT_REGIONAL:
              this.onEditRegional(entity, actionData);
              break;

            case bsDialogs.SET_STATUS_CONFIRM:
              this.onSetStatus(entity, {confirmStatus: actionData['confirmStatus']});
              break;

            default:
              break;
          }
        }
      }
    );
  }

  useRouteResolvedData() {
    this.checkBriefForMaster();
  }

  subscribeToStructure() {
    this.service.getShortStructure(SHORT_STRUCTURE_ADDITIONAL.businessStructure, true)
      .pipe(
        takeUntil(this.destroy$)
      )
      .subscribe((str) => {
        this.bsService.setStructure(str);

        if (Array.isArray(this.bsService.entities) && this.bsService.entities.length) {
          this.handleExpandCollapse(this.bsService.entities[0].id, true);
        }

        this.loading = false;
        this.cdr.markForCheck();
      });
  }

  onEditItem(entity: StructureEntityModel, ownSettings?: EntitySettingsModel) {
    this.dialogRef = this.dialog.open(MatEntityEditDialogComponent, {
      width: '700px',
      data: {entity, merchantTypeSchemas: this.merchantTypeSchemas, ownSettings},
      disableClose: true
    });

    this.dialogRef.afterClosed()
      .pipe(
        filter(val => !!val),
        tap(() => {
          this.entityLoading = true;
          this.cdr.detectChanges();
        }),
        takeUntil(this.destroy$),
      ).subscribe(data => {
      this.onCreateEditEntity(data);
    });
  }

  onEditItemMerchant(entity: StructureEntityModel) {
    combineLatest([
      this.service.getItemWithMerchantData(entity.path),
      this.entitySettingsService.getSettings(entity.path)
    ])
      .pipe(
        map(([merchantEntity, settings]) => ({...merchantEntity, settings})),
        mergeMap(merchantEntity => this.merchantTypesService
          .get(merchantEntity.merchant.type, merchantEntity.path).pipe(
            tap((merchantTypeSchema) => {
              this.merchantTypeSchemas = merchantTypeSchema ? [merchantTypeSchema] : [];
            }),
            map(() => merchantEntity)
          )),
        takeUntil(this.destroy$)
      ).subscribe(merchantEntity => {
        entity.update({merchant: merchantEntity.merchant, settings: merchantEntity.settings});

        this.onEditItem(entity);
      }
    );
  }

  onEditItemBrand(ent: StructureEntityModel) {
    this.provideSettings(ent)
      .pipe(
        takeUntil(this.destroy$)
      ).subscribe(({entity, ownSettings}) => {
      this.onEditItem(entity, ownSettings);
    });
  }

  onEditItemReseller(ent: StructureEntityModel) {
    this.provideSettings(ent).pipe(takeUntil(this.destroy$))
      .subscribe(({entity, ownSettings}) => {
        this.onEditItem(entity, ownSettings);
      });
  }

  onAddItem(entity: StructureEntityModel) {
    this.merchantTypesService.fetch(entity.path)
      .pipe(take(1))
      .subscribe((data) => {
        this.merchantTypeSchemas = data;
        const newEntity = new StructureEntityModel({
          parent: entity.path,
          parentId: entity.id,
          getEntityParent: () => entity
        });
        this.onEditItem(newEntity);
      });
  }

  onFindChange({value, checked}: { value: string, checked: boolean },
               fondedEntityId?: string, position?: number, scrollByText?: boolean) {
    if (!value) {
      this.foundIndex = -1;
      this.foundIds = [];
      return;
    }

    this.foundIds = this.getSearchEntityIds(value, checked);
    this.foundIndex = -1;
    this.businessStructureSearchService.setText(value);

    if (this.foundIds.length !== 0) {
      this.onFind((fondedEntityId ? 0 : 1), fondedEntityId, position, scrollByText);
    }
  }

  onFind(step: number, fondedEntityId?: string, position?: number, scrollByText: boolean = true) {
    this.foundIndex = typeof position === 'number' ? position : this.foundIds.length
      ? (this.foundIds.length + this.foundIndex + step) % this.foundIds.length
      : -1;

    this.expandToRoot(fondedEntityId || this.foundIds[this.foundIndex]);

    this.businessStructureSearchService.setPosition(this.foundIndex);
    this.businessStructureSearchService.setFoundedEntityId(fondedEntityId || this.foundIds[this.foundIndex]);

    if (scrollByText) {
      this.scrollByText();
    }
  }

  onStateFindChange(state: GlobalFinderState) {
    timer(1)
      .pipe(take(1))
      .subscribe(() => {
        this.onFindChange({value: state.text, checked: state.checked}, state.id, state.position);
      });
  }

  onFindClose() {
    this.foundIndex = -1;
    this.foundIds = [];
    this.businessStructureSearchService.clearState();
  }

  closeChildren(current: string) {
    this.bsService.expandedEntities.set(current, false);

    const {children} = this.bsService.entitiesObject[current];

    children.forEach((id: string) => {
      this.closeChildren(id);
    });
  }

  handleExpandCollapse(id: string, force?: boolean) {
    const isExpanded = !this.bsService.expandedEntities.get(id) || force;

    if (isExpanded) {
      this.bsService.expandedEntities.set(id, true);
    } else {
      this.closeChildren(id);
    }

    this.expandFounded();
  }

  private expandFounded() {
    this.entities = this.bsService.entities.filter((item: any, index: number) => {
      return this.bsService.expandedEntities.get(item.id) || this.bsService.expandedEntities.get(item.parentId) || !index;
    });

    this.entitySuspendedStatus();
  }

  private expandToRoot(id: string) {
    if (id) {
      this.bsService.expandedEntities.set(id, true);
      return this.expandToRoot(this.bsService.entitiesObject[id].parentId);
    }
    this.expandFounded();
  }

  private getSearchEntityIds(input: string, idOnly: boolean): string[] {
    if (idOnly) {
      const id = this.bsService.entities.find((ent) => ent.decryptedBrand.toString() === input)?.id;
      return id ? [id] : [];
    }

    const fields = ['name', 'title', 'path', 'key', 'decryptedBrand', 'id'];
    const ids = [];

    this.bsService.entities.forEach((value: Entity) => {
      const has = fields.some(field => {
        return value[field] && value[field].toString().toLowerCase().includes(input.toLowerCase());
      });

      if (has) {
        ids.push(value.id);
      }
    });

    return ids;
  }

  private getScrollIndex(input: string): number {
    let indexes: number = 0;
    this.entities.forEach((entity, index) => {
      if (entity.id === input) {
        indexes = index;
      }
    });

    return indexes;
  }

  private scrollByText() {
    const foundEntityIndex = this.getScrollIndex(this.foundIds[this.foundIndex]) || 0;
    this.viewPort?.scrollToIndex(foundEntityIndex, 'smooth');
    this.cdr.detectChanges();
  }

  private provideSettings(entity): Observable<{ entity: StructureEntityModel, ownSettings: EntitySettingsModel }> {
    const settings$ = iif(() => !entity.settings,
      this.entitySettingsService.getSettings(entity.path),
      of(entity.settings)
    );
    const ownSettings$ = this.entitySettingsService.getSettings(entity.path, true);

    return zip(settings$, ownSettings$)
      .pipe(
        map(([settings, ownSettings]) => {
          entity.settings = settings;
          return {entity, ownSettings};
        })
      );
  }

  private onSetStatus(dialogEntity, data: { confirmStatus: string }) {
    this.dialogRef = this.dialog.open(StatusConfirmComponent, {
      width: '400px',
      data: {entity: dialogEntity, confirmStatus: data.confirmStatus},
      disableClose: true
    });

    this.dialogRef.afterClosed()
      .pipe(
        filter((closeData) => typeof closeData.entity !== 'undefined'),
        take(1)
      )
      .subscribe(({entity, confirmStatus}) => {
        let statusChange$;
        if (confirmStatus === 'maintenance' || (entity.status === 'maintenance' && confirmStatus === 'normal')) {
          statusChange$ = this.service.setMaintenance(entity, confirmStatus);
        } else if (confirmStatus === 'suspended' || (entity.status === 'suspended' && confirmStatus === 'normal')) {
          statusChange$ = this.service.setStatus(entity, confirmStatus);
        } else if (confirmStatus === 'test' || (entity.status === 'test' && confirmStatus === 'normal')) {
          statusChange$ = this.service.setTest(entity, confirmStatus);
        } else if ('blocked_by_admin' === confirmStatus
          || ('blocked_by_admin' === entity.status && confirmStatus === 'normal')
        ) {
          statusChange$ = this.service.patchEntityStatus(entity, confirmStatus);
        }

        if (statusChange$) {
          statusChange$
            .pipe(
              switchMap(() => this.bsService.updateEntityStructure(true)),
              takeUntil(this.destroy$)
            ).subscribe(() => {
            this.expandFounded();
            this.cdr.detectChanges();
          });
        }
      });
  }

  private onEditRegional(dialogEntity, actionData) {
    this.dialog.open(MatRegionalDialogComponent, {
      width: '80vw',
      data: {
        ...dialogEntity,
        activeTab: actionData,
        entityParentPath: dialogEntity.parentId ? this.bsService.entitiesObject[dialogEntity.parentId].path : dialogEntity.path
      },
      disableClose: true
    }).afterClosed().pipe(
      switchMap(() => this.bsService.updateEntityStructure(true)),
      take(1)).subscribe(() => {
      this.expandFounded();
      this.cdr.detectChanges();
    });
  }

  private onCreateEditEntity(data: EditEntitySubmitData) {
    const entity = new Entity(data.entity);
    const updating: boolean = !!entity.key;
    const settingsUpdate = {};
    let applyEntitySource$: Observable<Entity>;

    if (entity.type === Entity.TYPE_MERCHANT) {
      applyEntitySource$ = updating
        ? this.service.updateMerchantEntityItem(entity.asUpdateMerchantData(), entity.path)
        : this.service.createMerchantEntityItem(entity.asCreateMerchantData(), entity.entityParent.path);
    } else {
      applyEntitySource$ = updating
        ? this.service.updateEntityItem(entity)
        : this.service.createEntityItem(entity, entity.entityParent.path);
    }

    applyEntitySource$ = applyEntitySource$.pipe(
      this.addEntityPath(entity.path)
    );

    if (entity.type === Entity.TYPE_BRAND || entity.type === Entity.TYPE_MERCHANT) {
      settingsUpdate['storePlayerInfo'] = data.entity.settings.storePlayerInfo;
    }

    if (entity.type === Entity.TYPE_BRAND || entity.type === Entity.TYPE_ENTITY) {
      settingsUpdate['isPlayerPasswordChangeEnabled'] = data.entity.settings.isPlayerPasswordChangeEnabled;
    }

    if (entity.type === Entity.TYPE_BRAND) {
      settingsUpdate['playerPrefix'] = data.entity.settings.playerPrefix;
    }

    if (settingsUpdate && Object.keys(settingsUpdate).length) {
      applyEntitySource$ = applyEntitySource$.pipe(
        switchMap((newEntity) => {
          return this.entitySettingsService.patchSettings(settingsUpdate, newEntity.path)
            .pipe(mapTo(newEntity));
        }));
    }

    if (data.addGames) {
      applyEntitySource$ = applyEntitySource$.pipe(
        this.addGames(entity.entityParent)
      );
    }

    applyEntitySource$.pipe(
      tap(() => {
        if (updating) {
          this.notifications.success(this.translation.instant('BUSINESS_STRUCTURE.notificationEdited', {name: entity.name}));
        }
      }),
      catchError((err) => {
        this.service.handleErrors.call(this.service, err);
        return throwError(err);
      }),
      tap((appliedEntity: Entity) => {
        if (!updating) {
          this.dialog.open(SetupHintDialogComponent, {
            width: '700px',
            data: {entity: appliedEntity},
            disableClose: true
          });
        }
      }),
      switchMap(() => this.bsService.updateEntityStructure(true)),
      finalize(() => {
        this.entityLoading = false;
        this.cdr.detectChanges();
      }),
      takeUntil(this.destroy$),
    ).subscribe(() => {
        if (updating) {
          this.dialogRef.close();
        }
        this.expandFounded();
      }
    );
  }

  private addGames(entityParent: Entity): OperatorFunction<Entity, Entity> {
    return mergeMap((newEntity: Entity) => {
      return this.gameService.getAllGames(entityParent.path, true, true).pipe(
        map(games => games.map(game => game.code)),
        filter((games: string []) => !!games.length),
        mergeMap(codes => this.gameService.setEntityGames(codes, newEntity.path).pipe(
          mapTo(newEntity)
        ))
      );
    });
  }

  private addEntityPath(builtPath: string): OperatorFunction<Entity, Entity> {
    return map((newEntity: Entity) => {
      if (typeof newEntity.path === 'undefined' && builtPath !== '') {
        newEntity.path = builtPath;
      }
      return newEntity;
    });
  }

  private setPanelActions() {
    this.panelActions = [
      <PanelAction>{
        title: 'BUSINESS_STRUCTURE.btnExpand',
        actionFn: () => this.expandAll(),
        getStyle: () => {
          return {color: '#1373d5'};
        }
      },
      <PanelAction>{
        title: 'BUSINESS_STRUCTURE.searchByDomain',
        color: 'primary',
        icon: 'search',
        availableFn: () => this.isSuperAdmin(),
        actionFn: () => this.searchByDomain(),
        disabledFn: () => this.loading,
      },
      <PanelAction>{
        title: '',
        hover: 'Download CSV',
        icon: 'get_app',
        getStyle: () => {
          return {
            width: '20px',
            padding: '0',
            'min-width': '20px'
          };
        },
        availableFn: () => this.isSuperAdmin(),
        actionFn: () => this.downloadCSV(),
        disabledFn: () => this.loading,
      }
    ];
  }

  private expandAll() {
    this.bsService.entities.forEach(({id}) => {
      this.bsService.expandedEntities.set(id, true);
    });

    this.expandFounded();
  }

  private downloadCSV() {
    this.bsService.downloadCSV();
  }

  private searchByDomain() {
    this.dialogRef = this.dialog.open(SearchByDomainModalComponent, {
      width: '500px',
      disableClose: true
    });
  }

  private checkBriefForMaster() {
    let briefData = this.route.snapshot.data['brief'];
    if ('name' in briefData && briefData['name'] === Entity.MASTER_NAME) {
      briefData['path'] = Entity.ROOT_PATH;
    }
  }

  private entitySuspendedStatus() {
    this.entities.map((entity: Entity) => {
      const parent: Entity = entity.parentId ? this.bsService.entitiesObject[entity.parentId] : entity;
      const isParentBlocked = parent.status === 'blocked_by_admin' || parent.status === 'suspended';
      const isEntityBlocked = entity.status === 'blocked_by_admin' || entity.status === 'suspended';

      if (isParentBlocked || parent.changeStatusState === 'blocked_by_parent') {
        entity.changeStatusState = 'blocked_by_parent';
      } else if (isEntityBlocked) {
        entity.changeStatusState = 'blocked';
      }
    });
  }
}
