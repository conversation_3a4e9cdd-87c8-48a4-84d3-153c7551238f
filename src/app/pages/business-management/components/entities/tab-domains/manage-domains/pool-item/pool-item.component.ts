import { Component, Input } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { combineLatest } from 'rxjs';
import { filter, finalize, switchMap, take } from 'rxjs/operators';
import { DOMAIN_TYPES, DomainPool } from '../../../../../../../common/models/domain.model';
import { Entity } from '../../../../../../../common/models/entity.model';
import { DomainsPoolService } from '../../../../../../domains-management/domains-pool/domains-pool.service';
import { EntityDomainPoolService } from '../entity-domain-pool.service';
import { SelectPoolDialogComponent, SelectPoolDialogData } from '../select-pool-dialog/select-pool-dialog.component';

@Component({
  selector: 'pool-item',
  templateUrl: './pool-item.component.html',
  styleUrls: ['./pool-item.component.scss'],
})
export class PoolItemComponent {
  @Input() entity: Entity;

  domainPools: DomainPool[];
  domainPool: DomainPool;
  loading = true;

  private resetComplete = false;

  constructor(private readonly poolService: DomainsPoolService,
              private readonly entityPoolService: EntityDomainPoolService,
              private readonly dialog: MatDialog) {
  }

  ngOnInit() {
    combineLatest([
      this.poolService.getList(DOMAIN_TYPES.static),
      this.entityPoolService.get(this.entity.path, true)
    ]).pipe(
      finalize(() => {
        this.loading = false;
      }),
      take(1)
    ).subscribe(([domainPools, domainPool]) => {
      this.domainPools = domainPools;
      this.domainPool = domainPool;
    });
  }

  resetButtonDisabled(): boolean {
    return !(this.domainPool && this.domainPool.hasOwnProperty('id')) || this.resetComplete;
  }

  resetToParent(event: Event) {
    event.preventDefault();
    if (this.resetButtonDisabled()) {
      return;
    }
    this.entityPoolService.remove(this.entity.path).pipe(
      take(1),
    ).subscribe(() => {
      this.domainPool = undefined;
      this.resetComplete = true;
    });
  }

  setNewDomain() {
    const data: SelectPoolDialogData = {
      domainPools: this.domainPools,
      domainPool: this.domainPool,
    };
    this.dialog.open(SelectPoolDialogComponent, {
      data: data,
      disableClose: true,
      width: '600px'
    }).afterClosed().pipe(
      filter(result => !!result),
      switchMap(({id}) => this.entityPoolService.set(id, this.entity.path)),
      take(1),
    ).subscribe((result) => {
      this.domainPool = result;
      this.resetComplete = false;
    });
  }
}
