import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

import { PERMISSIONS_LIST, PERMISSIONS_NAMES } from '../../app.constants';
import { BriefResolver } from '../../common/services/resolvers/brief.resolver';
import { BusinessManagementComponent } from './business-management.component';
import { MatBusinessStructureComponent } from './components/mat-business-structure/mat-business-structure.component';


const routes: Routes = [
  {
    path: '',
    component: BusinessManagementComponent,
    children: [
      {
        path: 'business-structure',
        component: MatBusinessStructureComponent,
        data: {
          title: 'Business Structure',
          permissions: [PERMISSIONS_NAMES.ENTITY_VIEW],
        },
        resolve: {
          brief: BriefResolver,
        }
      },
      {
        path: 'entities',
        loadChildren: () => import('./components/entities/entities.module').then(m => m.EntitiesModule),
        data: {
          permissions: PERMISSIONS_LIST.ENTITY,
        },
      },
    ]
  },
  {
    path: 'entity-bulk-actions',
    loadChildren: () => import('./components/entity-bulk-actions/entity-bulk-actions.module')
      .then(m => m.EntityBulkActionsModule),
    data: {
      title: 'Entities Bulk Actions',
      permissions: [PERMISSIONS_NAMES.ENTITY_VIEW],
    },
  },
  {
    path: 'bi-reports-switch',
    loadChildren: () => import('./components/bi-reports-switch/bi-reports-switch.module')
      .then(m => m.BiReportsSwitchModule),
    data: {
      title: 'BI Reports Switch',
      permissions: [PERMISSIONS_NAMES.KEYENTITY_BI_REPORT_DOMAINS, PERMISSIONS_NAMES.KEYENTITY_BI_REPORT_DOMAINS_VIEW],
    },
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class BusinessManagementRoutingModule {
}
