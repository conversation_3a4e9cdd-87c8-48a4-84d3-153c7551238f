import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { BsDropdownModule } from 'ngx-bootstrap/dropdown';
import { TabsModule } from 'ngx-bootstrap/tabs';

import { CountryService } from '../../common/services/country.service';
import { CurrencyService } from '../../common/services/currency.service';
import { LanguagesService } from '../../common/services/languages.service';
import { BusinessManagementComponent } from './business-management.component';
import { BusinessManagementRoutingModule } from './business-management.routing';
import { MatBusinessStructureModule } from './components/mat-business-structure/mat-business-structure.module';
import { SetupEntityGuard } from './components/entities/setup-entity.guard';
import { EntityStateService } from './entity-state.service';


@NgModule({
  imports: [
    CommonModule,
    BsDropdownModule.forRoot(),
    TabsModule.forRoot(),
    MatBusinessStructureModule,
    BusinessManagementRoutingModule,
  ],
  declarations: [
    BusinessManagementComponent
  ],
  providers: [
    CountryService,
    CurrencyService,
    LanguagesService,
    EntityStateService,
    SetupEntityGuard,
  ]
})
export class BusinessManagementModule {

}
