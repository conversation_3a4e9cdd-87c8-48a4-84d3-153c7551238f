import { ChangeDetectionStrategy, Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatTableDataSource } from '@angular/material/table';
import { Domain, DomainPool, DomainType } from '../../../../common/models/domain.model';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { MatCheckboxChange } from '@angular/material/checkbox';

export interface DomainsPoolDialogData {
  pool?: DomainPool;
  domains: Domain[];
  poolType?: DomainType;
}

interface DomainPoolItem {
  code: string;
  displayName: string;
  type: 'lobby' | 'game';
  selected: boolean;
  enabled: boolean;
  active: boolean;
}

const compareRow = (row: DomainPoolItem) => (item: DomainPoolItem): boolean => item.type === row.type && item.code === row.code;

@Component({
  selector: 'domains-pool-dialog',
  templateUrl: './domains-pool-dialog.component.html',
  styleUrls: ['./domains-pool-dialog.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DomainsPoolDialogComponent implements OnInit {
  dataSource: MatTableDataSource<DomainPoolItem>;
  displayedColumns: string[] = ['code', 'name', 'active', 'type'];
  submitted = false;
  poolType: DomainType = 'static';

  private readonly form: FormGroup;
  private readonly items: DomainPoolItem[] = [];

  constructor(
    @Inject(MAT_DIALOG_DATA) {pool, domains, poolType}: DomainsPoolDialogData,
    fb: FormBuilder,
    private readonly dialogRef: MatDialogRef<DomainsPoolDialogComponent>,
  ) {
    this.poolType = poolType || 'static';
    for (const staticDomain of domains) {
      const record = pool && pool.domains.find(({id}) => staticDomain.id === id);
      this.items.push({
        code: staticDomain.id || '',
        type: 'game',
        displayName: staticDomain.domain || '',
        selected: Boolean(record),
        active: Boolean(record) && Boolean(record.isActive),
        enabled: true
      });
    }

    this.form = fb.group({
      name: [pool && pool.name ? pool.name : '', Validators.required],
    });
  }

  ngOnInit(): void {
    this.dataSource = new MatTableDataSource(this.items);
  }

  get selectedItems(): DomainPoolItem[] {
    return this.dataSource.data.filter(item => item.selected);
  }

  get nameControl(): FormControl {
    return this.form.get('name') as FormControl;
  }

  rowSelectedChanged(changeEvent: MatCheckboxChange, row: DomainPoolItem) {
    if (!changeEvent.checked) {
      const compareFn = compareRow(row);
      const record = this.dataSource.data.find(compareFn);
      if (record) {
        record.active = false;
      }
    }
  }

  rowActiveChanged(changeEvent: MatCheckboxChange, row: DomainPoolItem) {
    if (changeEvent.checked) {
      const compareFn = compareRow(row);
      const record = this.dataSource.data.find(compareFn);
      if (record) {
        record.selected = true;
      }
    }
  }

  submit() {
    this.form.markAllAsTouched();
    if (this.form.valid) {
      this.dialogRef.close({
        name: this.nameControl.value,
        domains: this.selectedItems.filter(({ type }) => type === 'game').map(item => ({
          id: item.code,
          isActive: item.active
        }))
      });
    }
  }
}
