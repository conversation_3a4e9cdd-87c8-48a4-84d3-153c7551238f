import { SwuiGridField } from '@skywind-group/lib-swui';

const SCHEMA: SwuiGridField[] = [
  {
    field: 'name',
    title: 'DOMAINS.GRID.name',
    type: 'string',
    isList: true,
    isViewable: false,
    isSortable: false,
  },
  {
    field: 'createdAt',
    title: 'DOMAINS.GRID.created',
    type: 'datetimerange',
    dataSource: '',
    isList: true,
    isViewable: false,
    isSortable: true,
    td: {
      type: 'timestamp',
      nowrap: true
    },
  },
  {
    field: 'updatedAt',
    title: 'DOMAINS.GRID.updated',
    type: 'datetimerange',
    dataSource: '',
    isList: true,
    isViewable: true,
    isSortable: true,
    td: {
      type: 'timestamp',
      nowrap: true
    },
  },
];


export const SCHEMA_LIST = SCHEMA.filter(el => el.isList).map(el => {
  el.isListVisible = true;
  return el;
});
