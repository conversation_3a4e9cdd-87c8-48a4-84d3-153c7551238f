import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { PanelAction, SwuiNotificationsService } from '@skywind-group/lib-swui';
import { Subject } from 'rxjs';
import { filter, switchMap, takeUntil, tap } from 'rxjs/operators';
import { Domain, DOMAIN_TYPES, DomainsItemDialogData, DomainType } from '../../common/models/domain.model';
import { GameServerService } from '../game-server/game-server.service';
import { DomainsItemDialogComponent } from './domains/domains-item-dialog/domains-item-dialog.component';
import { DomainsManagementService } from './domains-management.service';
import { MatDialog } from '@angular/material/dialog';
import { MatTabChangeEvent } from '@angular/material/tabs';
import { DomainsPoolDialogComponent, DomainsPoolDialogData } from './domains-pool/dialog/domains-pool-dialog.component';
import { DomainsPoolService } from './domains-pool/domains-pool.service';
import { ActivatedRoute } from '@angular/router';

type PanelType = 'domain' | 'static-pool' | 'dynamic-pool';

@Component({
  selector: 'domains-management',
  templateUrl: './domains-management.component.html',
  providers: [
    DomainsManagementService,
    GameServerService,
  ],
})
export class DomainsManagementComponent implements OnInit, OnDestroy {
  readonly gameServers: string[];

  panelActions: PanelAction[] = [];
  selectedPanel: PanelType = 'static-pool';
  domainType: DomainType = 'static';
  staticDomains: Domain[] = [];
  dynamicDomains: Domain[] = [];

  private readonly _destroyed$ = new Subject();

  constructor(private readonly service: DomainsManagementService,
              private readonly poolService: DomainsPoolService,
              private readonly dialog: MatDialog,
              private readonly notifications: SwuiNotificationsService,
              private readonly translate: TranslateService,
              { snapshot: { data: { gameServers } } }: ActivatedRoute) {
    this.gameServers = gameServers.map(({ name }) => name);
  }

  ngOnInit() {
    this.setPanelActions();
    this.service.getList(DOMAIN_TYPES.static).pipe(takeUntil(this._destroyed$)).subscribe(items => {
      this.staticDomains = items;
    });
    this.service.getList(DOMAIN_TYPES.dynamic).pipe(takeUntil(this._destroyed$)).subscribe(items => {
      this.dynamicDomains = items;
    });
  }

  ngOnDestroy() {
    this._destroyed$.next();
    this._destroyed$.complete();
  }

  onTabChange(event: MatTabChangeEvent) {
    if (event.index === 0) {
      this.selectedPanel = 'static-pool';
    } else if (event.index === 1) {
      this.selectedPanel = 'dynamic-pool';
    } else if (event.index === 2) {
      this.selectedPanel = 'domain';
      this.domainType = DOMAIN_TYPES.static;
    } else if (event.index === 3) {
      this.selectedPanel = 'domain';
      this.domainType = DOMAIN_TYPES.dynamic;
    }
    this.setPanelActions();
  }

  private setPanelActions() {
    if (this.selectedPanel === 'static-pool') {
      this.panelActions = [{
        title: 'DOMAINS.addPool',
        color: 'primary',
        icon: 'add',
        actionFn: () => {
          const data: DomainsPoolDialogData = {
            domains: this.staticDomains,
            poolType: 'static'
          };
          const dialogRef = this.dialog.open(DomainsPoolDialogComponent, {
            width: '500px',
            data,
            disableClose: true
          });
          dialogRef.afterClosed()
            .pipe(
              filter(record => !!record),
              switchMap(record => this.poolService.create(DOMAIN_TYPES.static, record)),
              switchMap(pool => this.translate.get('DOMAINS.notificationPoolCreated', { name: pool.name })),
              tap(message => this.notifications.success(message)),
              takeUntil(this._destroyed$)
            )
            .subscribe(() => {
              this.poolService.isGridChanged$.next();
            });
        },
      }];
    } else if (this.selectedPanel === 'dynamic-pool') {
      this.panelActions = [{
        title: 'DOMAINS.addDynamicPool',
        color: 'primary',
        icon: 'add',
        actionFn: () => {
          const data: DomainsPoolDialogData = {
            domains: this.dynamicDomains,
            poolType: 'dynamic'
          };
          const dialogRef = this.dialog.open(DomainsPoolDialogComponent, {
            width: '500px',
            data,
            disableClose: true
          });
          dialogRef.afterClosed()
            .pipe(
              filter(record => !!record),
              switchMap(record => this.poolService.create(record, 'dynamic')),
              switchMap(pool => this.translate.get('DOMAINS.notificationPoolCreated', { name: pool.name })),
              tap(message => this.notifications.success(message)),
              takeUntil(this._destroyed$)
            )
            .subscribe(() => {
              this.poolService.isGridChanged$.next();
            });
        },
      }];
    } else {
      this.panelActions = [{
        title: 'DOMAINS.addDomain',
        color: 'primary',
        icon: 'add',
        actionFn: () => {
          const data: DomainsItemDialogData = {
            gameServers: this.gameServers,
            type: this.domainType
          };
          const dialogRef = this.dialog.open(DomainsItemDialogComponent, {
            width: '500px',
            data,
            disableClose: true
          });
          dialogRef.afterClosed()
            .pipe(
              filter(domain => !!domain),
              switchMap((domain: Domain) => this.service.create(domain, this.domainType)),
              switchMap((domain: Domain) => this.translate.get('DOMAINS.notificationCreated', { domain: domain.domain })),
              tap(message => this.notifications.success(message)),
              takeUntil(this._destroyed$)
            )
            .subscribe(() => {
              this.service.isGridChanged$.next(this.domainType);
            });
        },
      }];
    }
  }
}
