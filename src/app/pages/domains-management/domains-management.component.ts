import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { PanelAction, SwuiNotificationsService } from '@skywind-group/lib-swui';
import { Subject } from 'rxjs';
import { filter, switchMap, takeUntil, tap } from 'rxjs/operators';
import { Domain, DOMAIN_TYPES, DomainsItemDialogData, DomainType } from '../../common/models/domain.model';
import { GameServerService } from '../game-server/game-server.service';
import { DomainsItemDialogComponent } from './domains/domains-item-dialog/domains-item-dialog.component';
import { DomainsManagementService } from './domains-management.service';
import { MatDialog } from '@angular/material/dialog';
import { MatTabChangeEvent } from '@angular/material/tabs';
import { ActivatedRoute } from '@angular/router';

type PanelType = 'domain' | 'static-pool' | 'dynamic-pool';

@Component({
  selector: 'domains-management',
  templateUrl: './domains-management.component.html',
  providers: [
    DomainsManagementService,
    GameServerService,
  ],
})
export class DomainsManagementComponent implements OnInit, OnDestroy {
  readonly gameServers: string[];

  panelActions: PanelAction[] = [];
  selectedPanel: PanelType = 'static-pool';
  domainType: DomainType = 'static';

  private readonly _destroyed$ = new Subject();

  constructor(private readonly service: DomainsManagementService,
              private readonly dialog: MatDialog,
              private readonly notifications: SwuiNotificationsService,
              private readonly translate: TranslateService,
              { snapshot: { data: { gameServers } } }: ActivatedRoute) {
    this.gameServers = gameServers.map(({ name }) => name);
  }

  ngOnInit() {
    this.setPanelActions();
  }

  ngOnDestroy() {
    this._destroyed$.next();
    this._destroyed$.complete();
  }

  onTabChange(event: MatTabChangeEvent) {
    if (event.index === 0) {
      this.selectedPanel = 'static-pool';
    } else if (event.index === 1) {
      this.selectedPanel = 'dynamic-pool';
    } else if (event.index === 2) {
      this.selectedPanel = 'domain';
      this.domainType = DOMAIN_TYPES.static;
    } else if (event.index === 3) {
      this.selectedPanel = 'domain';
      this.domainType = DOMAIN_TYPES.dynamic;
    }
    this.setPanelActions();
  }

  private setPanelActions() {
    if (this.selectedPanel === 'static-pool') {
      this.panelActions = [];
    } else if (this.selectedPanel === 'dynamic-pool') {
      this.panelActions = [];
    } else {
      this.panelActions = [{
        title: 'DOMAINS.addDomain',
        color: 'primary',
        icon: 'add',
        actionFn: () => {
          const data: DomainsItemDialogData = {
            gameServers: this.gameServers,
            type: this.domainType
          };
          const dialogRef = this.dialog.open(DomainsItemDialogComponent, {
            width: '500px',
            data,
            disableClose: true
          });
          dialogRef.afterClosed()
            .pipe(
              filter(domain => !!domain),
              switchMap((domain: Domain) => this.service.create(domain, this.domainType)),
              switchMap((domain: Domain) => this.translate.get('DOMAINS.notificationCreated', { domain: domain.domain })),
              tap(message => this.notifications.success(message)),
              takeUntil(this._destroyed$)
            )
            .subscribe(() => {
              this.service.isGridChanged$.next(this.domainType);
            });
        },
      }];
    }
  }
}
